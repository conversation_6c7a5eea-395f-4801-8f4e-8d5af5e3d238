import { motion } from "framer-motion";
import { Hero } from "../components/Hero";
import { FeaturedRestaurants } from "../components/FeaturedRestaurants";
import { Categories } from "../components/Categories";
import { Features } from "../components/Features";
import { HowItWorks } from "../components/HowItWorks";
import { Testimonials } from "../components/Testimonials";
import { Statistics } from "../components/Statistics";
import { CallToAction } from "../components/CallToAction";
import { Newsletter } from "@/components/Newsletter";
import { AIChat } from "@/components/AIChat";
import { PersonalizedRecommendations } from "@/components/personalizedrecommendations";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.3,
      delayChildren: 0.2,
    },
  },
};

const sectionVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

export const Home = () => {
  return (
    <>
      <motion.main
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Hero />
        <motion.div variants={sectionVariants}>
          <FeaturedRestaurants />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <PersonalizedRecommendations />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <Categories />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <Features />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <HowItWorks />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <Testimonials />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <Statistics />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <CallToAction />
        </motion.div>
        <motion.div variants={sectionVariants}>
          <Newsletter />
        </motion.div>
      </motion.main>

      {/* Add the AI Chat component */}
      <AIChat defaultLanguage="en" />
    </>
  );
};
