@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }

  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.leaflet-container {
  width: 100%;
  height: 100%;
  z-index: 1;
}

.leaflet-control-container {
  z-index: 999 !important;
}

.leaflet-pane {
  z-index: 1;
}

.leaflet-top,
.leaflet-bottom {
  z-index: 999 !important;
}

.leaflet-popup {
  z-index: 999 !important;
}

.leaflet-control {
  z-index: 999 !important;
}

[role="dialog"] {
  z-index: 9999 !important;
}

/* Ensure Select dropdowns appear above dialogs */
[data-radix-select-content] {
  z-index: 99999 !important;
}

.rdp {
  position: relative;
  z-index: 100 !important;
}

.rdp-months {
  background-color: white;
}

.rdp-day {
  width: 36px !important;
  height: 36px !important;
  border-radius: 50%;
}

.rdp-day_selected {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

.rdp-day_today {
  background-color: hsl(var(--accent)) !important;
  color: hsl(var(--accent-foreground)) !important;
}

.rdp-day_range_middle {
  background-color: hsl(var(--accent) / 0.5) !important;
  color: hsl(var(--accent-foreground)) !important;
}

[data-radix-popper-content-wrapper] {
  z-index: 200 !important;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
}

@layer utilities {
  .hero-grid-bg::before {
    content: "";
    position: absolute;
    inset: 0;

    z-index: 0;

    --grid-color: hsl(var(--border) / 0.08);
    --dot-color: hsl(var(--muted-foreground) / 0.15);
    --grid-size: 40px;

    background-image: linear-gradient(
        to right,
        var(--grid-color) 1px,
        transparent 1px
      ),
      linear-gradient(to bottom, var(--grid-color) 1px, transparent 1px),
      radial-gradient(circle at center, var(--dot-color) 1.5px, transparent 2px);
    background-size: var(--grid-size) var(--grid-size),
      var(--grid-size) var(--grid-size), var(--grid-size) var(--grid-size);
    background-position: -1px -1px, -1px -1px,
      calc(var(--grid-size) / 2 - 1px) calc(var(--grid-size) / 2 - 1px);
  }

  @keyframes blob {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(30px, -50px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }
  .animate-blob {
    animation: blob 7s infinite ease-in-out;
  }
  .animation-delay-2000 {
    animation-delay: 2s;
  }
  .animation-delay-4000 {
    animation-delay: 4s;
  }
}
