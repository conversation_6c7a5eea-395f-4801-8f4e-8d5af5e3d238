import { Utensils } from "lucide-react";
import { <PERSON> } from "react-router-dom";

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="font-sans tracking-wide bg-white p-4">
      <div className="flex flex-wrap justify-between gap-10">
        <div className="max-w-md">
          <Link
            className="flex items-center space-x-2"
            to="/"
          >
            <Utensils className="h-6 w-6" />
            <span className="text-xl font-bold">Qonai</span>
          </Link>
          <div className="mt-6">
            <p className="text-gray-600 leading-relaxed text-sm">
              Qonai is a modern restaurant platform that combines social media
              features with AI-powered digital menus. Experience a new way of
              dining where you can discover dishes, get personalized
              recommendations, and share your culinary journey with others.
            </p>
          </div>
          <ul className="mt-10 flex space-x-5">
            <li>
                <a href="#" onClick={(e) => e.preventDefault()} aria-label="Facebook">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="fill-blue-600 w-8 h-8"
                  viewBox="0 0 49.652 49.652"
                >
                  <path
                    d="M24.826 0C11.137 0 0 11.137 0 24.826c0 13.688 11.137 24.826 24.826 24.826 13.688 0 24.826-11.138 24.826-24.826C49.652 11.137 38.516 0 24.826 0zM31 25.7h-4.039v14.396h-5.985V25.7h-2.845v-5.088h2.845v-3.291c0-2.357 1.12-6.04 6.04-6.04l4.435.017v4.939h-3.219c-.524 0-1.269.262-1.269 1.386v2.99h4.56z"
                    data-original="#000000"
                  />
                </svg>
              </a>
            </li>
            <li>
                <a href="#" onClick={(e) => e.preventDefault()} aria-label="Instagram">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-8 h-8"
                  viewBox="0 0 152 152"
                >
                  <linearGradient
                    id="a"
                    x1="22.26"
                    x2="129.74"
                    y1="22.26"
                    y2="129.74"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop offset="0" stopColor="#fae100" />
                    <stop offset=".15" stopColor="#fcb720" />
                    <stop offset=".3" stopColor="#ff7950" />
                    <stop offset=".5" stopColor="#ff1c74" />
                    <stop offset="1" stopColor="#6c1cd1" />
                  </linearGradient>
                  <g data-name="Layer 2">
                    <g data-name="03.Instagram">
                      <rect
                        width="152"
                        height="152"
                        fill="url(#a)"
                        data-original="url(#a)"
                        rx="76"
                      />
                      <g fill="#fff">
                        <path
                          fill="#ffffff10"
                          d="M133.2 26c-11.08 20.34-26.75 41.32-46.33 60.9S46.31 122.12 26 133.2q-1.91-1.66-3.71-3.46A76 76 0 1 1 129.74 22.26q1.8 1.8 3.46 3.74z"
                          data-original="#ffffff10"
                        />
                        <path
                          d="M94 36H58a22 22 0 0 0-22 22v36a22 22 0 0 0 22 22h36a22 22 0 0 0 22-22V58a22 22 0 0 0-22-22zm15 54.84A18.16 18.16 0 0 1 90.84 109H61.16A18.16 18.16 0 0 1 43 90.84V61.16A18.16 18.16 0 0 1 61.16 43h29.68A18.16 18.16 0 0 1 109 61.16z"
                          data-original="#ffffff"
                        />
                        <path
                          d="m90.59 61.56-.19-.19-.16-.16A20.16 20.16 0 0 0 76 55.33 20.52 20.52 0 0 0 55.62 76a20.75 20.75 0 0 0 6 14.61 20.19 20.19 0 0 0 14.42 6 20.73 20.73 0 0 0 14.55-35.05zM76 89.56A13.56 13.56 0 1 1 89.37 76 13.46 13.46 0 0 1 76 89.56zm26.43-35.18a4.88 4.88 0 0 1-4.85 4.92 4.81 4.81 0 0 1-3.42-1.43 4.93 4.93 0 0 1 3.43-8.39 4.82 4.82 0 0 1 3.09 1.12l.1.1a3.05 3.05 0 0 1 .44.44l.11.12a4.92 4.92 0 0 1 1.1 3.12z"
                          data-original="#ffffff"
                        />
                      </g>
                    </g>
                  </g>
                </svg>
              </a>
            </li>
            <li>
                <a href="#" onClick={(e) => e.preventDefault()} aria-label="Twitter/X">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-8 h-8"
                  viewBox="0 0 1227 1227"
                >
                  <path
                    d="M613.5 0C274.685 0 0 274.685 0 613.5S274.685 1227 613.5 1227 1227 952.315 1227 613.5 952.315 0 613.5 0z"
                    data-original="#000000"
                  />
                  <path
                    fill="#fff"
                    d="m680.617 557.98 262.632-305.288h-62.235L652.97 517.77 470.833 252.692H260.759l275.427 400.844-275.427 320.142h62.239l240.82-279.931 192.35 279.931h210.074L680.601 557.98zM345.423 299.545h95.595l440.024 629.411h-95.595z"
                    data-original="#ffffff"
                  />
                </svg>
              </a>
            </li>
          </ul>
        </div>

        <div className="max-lg:min-w-[140px]">
          <h4 className="text-gray-800 font-semibold text-base relative max-sm:cursor-pointer">
            Navigation
          </h4>
          <ul className="mt-6 space-y-4">
            <li>
                <Link
                to="/"
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Home
              </Link>
            </li>
            <li>
                <Link
                to="/restaurants"
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Restaurants
              </Link>
            </li>
            <li>
                <Link
                to="/about"
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                About
              </Link>
            </li>
            <li>
                <Link
                to="/pricing"
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Pricing
              </Link>
            </li>
          </ul>
        </div>

        <div className="max-lg:min-w-[140px]">
          <h4 className="text-gray-800 font-semibold text-base relative max-sm:cursor-pointer">
            Features
          </h4>
          <ul className="space-y-4 mt-6">
            <li>
                <a
                href="#"
                onClick={(e) => e.preventDefault()}
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                AI Recommendations
              </a>
            </li>
            <li>
                <a
                href="#"
                onClick={(e) => e.preventDefault()}
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Digital Menus
              </a>
            </li>
            <li>
                <a
                href="#"
                onClick={(e) => e.preventDefault()}
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Reservations
              </a>
            </li>
            <li>
                <a
                href="#"
                onClick={(e) => e.preventDefault()}
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Restaurant Dashboard
              </a>
            </li>
          </ul>
        </div>

        <div className="max-lg:min-w-[140px]">
          <h4 className="text-gray-800 font-semibold text-base relative max-sm:cursor-pointer">
            Account
          </h4>
          <ul className="space-y-4 mt-6">
            <li>
                <Link
                to="/auth"
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Login / Register
              </Link>
            </li>
            <li>
                <Link
                to="/profile"
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Profile
              </Link>
            </li>
            <li>
                <Link
                to="/dashboard"
                className="hover:text-gray-800 text-gray-600 text-sm"
                >
                Dashboard
              </Link>
            </li>
          </ul>
        </div>
      </div>

      <hr className="mt-10 mb-6 border-gray-300" />

      <div className="flex flex-wrap max-md:flex-col gap-4">
        <ul className="md:flex md:space-x-6 max-md:space-y-2">
          <li>
            <a
              href="#"
              onClick={(e) => e.preventDefault()}
              className="hover:text-gray-800 text-gray-600 text-sm"
            >
              Terms of Service
            </a>
          </li>
          <li>
            <a
              href="#"
              onClick={(e) => e.preventDefault()}
              className="hover:text-gray-800 text-gray-600 text-sm"
            >
              Privacy Policy
            </a>
          </li>
        </ul>
        <p className="text-gray-600 text-sm md:ml-auto">
          © {currentYear} Qonai. All rights reserved.
        </p>
      </div>
    </footer>
  );
}
