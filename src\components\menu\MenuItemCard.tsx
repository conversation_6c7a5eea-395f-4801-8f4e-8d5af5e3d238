import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MenuItem } from "@/types";
import { Link } from "react-router-dom";

interface MenuItemCardProps {
  menuItem: MenuItem;
}

export const MenuItemCard: React.FC<MenuItemCardProps> = ({ menuItem }) => {
  // Format price to display as currency
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(price);
  };

  return (
    <Link to={`/restaurants/${menuItem.restaurantId}/menu/${menuItem.itemId}`}>
      <Card className="overflow-hidden h-full transition-all duration-300 hover:shadow-lg">
        <div className="relative h-48 overflow-hidden">
          {menuItem.imageUrl ? (
            <img
              src={menuItem.imageUrl}
              alt={menuItem.name}
              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
            />
          ) : (
            <div className="w-full h-full bg-muted flex items-center justify-center">
              <span className="text-muted-foreground">No image</span>
            </div>
          )}

          {/* Price badge */}
          <div className="absolute top-2 right-2">
            <Badge className="bg-primary text-primary-foreground font-semibold">
              {formatPrice(menuItem.price)}
            </Badge>
          </div>

          {/* Special badges */}
          <div className="absolute bottom-2 left-2 flex flex-wrap gap-1">
            {menuItem.isSignatureDish && (
              <Badge variant="secondary" className="bg-amber-100 text-amber-800 border border-amber-200">
                Signature
              </Badge>
            )}
            {menuItem.isSeasonalDish && (
              <Badge variant="secondary" className="bg-green-100 text-green-800 border border-green-200">
                Seasonal
              </Badge>
            )}
          </div>
        </div>

        <CardContent className="p-4">
          <div className="space-y-2">
            <div>
              <h3 className="font-semibold text-lg line-clamp-1">{menuItem.name}</h3>
              <p className="text-muted-foreground text-sm line-clamp-2">
                {menuItem.description}
              </p>
            </div>

            {/* Category and dietary info */}
            <div className="flex flex-wrap gap-1 mt-2">
              {menuItem.category && (
                <Badge variant="outline" className="text-xs">
                  {menuItem.category}
                </Badge>
              )}

              {menuItem.dietary && menuItem.dietary.length > 0 &&
                menuItem.dietary.slice(0, 2).map((diet, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                  >
                    {diet}
                  </Badge>
                ))
              }

              {menuItem.dietary && menuItem.dietary.length > 2 && (
                <Badge variant="outline" className="text-xs">
                  +{menuItem.dietary.length - 2} more
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
};
