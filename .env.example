# Server Configuration
PORT=3000
NODE_ENV=development

# Email Configuration
# Options: gmail, mailtrap, smtp, sendgrid
EMAIL_PROVIDER=gmail
EMAIL_FROM=<EMAIL>
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Mailtrap Configuration (for testing)
MAILTRAP_USER=your_mailtrap_user
MAILTRAP_PASS=your_mailtrap_password

# SendGrid Configuration
SENDGRID_API_KEY=your_sendgrid_api_key

# Custom SMTP Configuration
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password

# Firebase Configuration
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_PRIVATE_KEY_ID=your_private_key_id
FIREBASE_PRIVATE_KEY=your_private_key
FIREBASE_CLIENT_EMAIL=your_client_email
FIREBASE_CLIENT_ID=your_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_CERT_URL=your_client_cert_url
FIREBASE_UNIVERSE_DOMAIN=googleapis.com

# API Authentication
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
API_KEY=your_api_key
LEGACY_API_KEY=your_legacy_api_key
