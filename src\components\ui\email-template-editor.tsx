import React, { useState, useEffect } from "react";
import { HtmlEditor } from "./html-editor";
import { toast } from "sonner";
import {
  TemplateBlocksGrid,
  TemplateBlock,
  templateBlocks,
} from "./email-template-blocks";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  Eye,
  Save,
  Plus,
  Palette,
  Settings2 as Settings,
  Undo,
  Redo,
  Download,
  Upload,
  Copy,
  Trash2,
  FileCode,
  Wand2,
  Maximize,
  Send,
  X,
} from "lucide-react";

// Define email template settings interface
interface EmailTemplateSettings {
  backgroundColor: string;
  contentWidth: number;
  fontFamily: string;
  fontSize: number;
  textColor: string;
  linkColor: string;
  buttonColor: string;
  buttonTextColor: string;
}

// Default email template settings
const defaultSettings: EmailTemplateSettings = {
  backgroundColor: "#f9f9f9",
  contentWidth: 600,
  fontFamily: "Arial, sans-serif",
  fontSize: 16,
  textColor: "#333333",
  linkColor: "#0066cc",
  buttonColor: "#ff6200",
  buttonTextColor: "#ffffff",
};

// Email template variables interface
interface EmailTemplateVariable {
  name: string;
  description: string;
  defaultValue: string;
}

// Common email template variables
const commonVariables: EmailTemplateVariable[] = [
  {
    name: "name",
    description: "Recipient's name",
    defaultValue: "John Doe",
  },
  {
    name: "unsubscribeLink",
    description: "Link to unsubscribe from emails",
    defaultValue: "#",
  },
  {
    name: "currentDate",
    description: "Current date",
    defaultValue: new Date().toLocaleDateString(),
  },
];

// Email Template Editor Props
interface EmailTemplateEditorProps {
  initialHtml?: string;
  onSave?: (html: string, variables: string[]) => void;
  onPreview?: (html: string) => void;
  className?: string;
}

export function EmailTemplateEditor({
  initialHtml = "",
  onSave,
  onPreview,
  className,
}: EmailTemplateEditorProps) {
  const [html, setHtml] = useState(initialHtml);
  const [settings, setSettings] =
    useState<EmailTemplateSettings>(defaultSettings);
  const [variables, setVariables] =
    useState<EmailTemplateVariable[]>(commonVariables);
  const [customVariables, setCustomVariables] = useState<
    EmailTemplateVariable[]
  >([]);
  const [history, setHistory] = useState<string[]>([initialHtml]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [showPreview, setShowPreview] = useState(false);
  const [previewHtml, setPreviewHtml] = useState("");
  const [showInlinePreview, setShowInlinePreview] = useState(false);

  // Add to history when HTML changes
  useEffect(() => {
    if (html !== history[historyIndex]) {
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(html);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  }, [html]);

  // Handle undo
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setHtml(history[historyIndex - 1]);
    }
  };

  // Handle redo
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setHtml(history[historyIndex + 1]);
    }
  };

  // Handle adding a template block
  const handleAddBlock = (block: TemplateBlock) => {
    setHtml((prevHtml) => prevHtml + block.html);
  };

  // Handle preview
  const handlePreview = () => {
    // Replace variables with their default values for preview
    let previewContent = html;

    [...variables, ...customVariables].forEach((variable) => {
      const regex = new RegExp(`\\{\\{${variable.name}\\}\\}`, "g");
      previewContent = previewContent.replace(regex, variable.defaultValue);
    });

    setPreviewHtml(previewContent);
    setShowPreview(true);

    if (onPreview) {
      onPreview(previewContent);
    }

    // Show toast notification
    toast({
      title: "Preview Generated",
      description: "Email preview has been generated with sample data.",
      duration: 2000,
    });
  };

  // Handle save
  const handleSave = () => {
    if (onSave) {
      // Extract all variables from the HTML
      const variableMatches = html.match(/\{\{([^}]+)\}\}/g) || [];
      const extractedVariables = variableMatches.map((match) =>
        match.replace(/\{\{|\}\}/g, "")
      );

      // Remove duplicates
      const uniqueVariables = [...new Set(extractedVariables)];

      onSave(html, uniqueVariables);
    }
  };

  // Handle adding a custom variable
  const handleAddCustomVariable = (
    name: string,
    description: string,
    defaultValue: string
  ) => {
    setCustomVariables([
      ...customVariables,
      { name, description, defaultValue },
    ]);
  };

  // Apply settings to HTML
  const applySettings = () => {
    // Create a wrapper with the settings
    const wrappedHtml = `
      <div style="
        font-family: ${settings.fontFamily};
        font-size: ${settings.fontSize}px;
        color: ${settings.textColor};
        background-color: ${settings.backgroundColor};
        margin: 0 auto;
        padding: 20px;
      ">
        <div style="
          max-width: ${settings.contentWidth}px;
          margin: 0 auto;
          background-color: #ffffff;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        ">
          ${html}
        </div>
      </div>
    `;

    setHtml(wrappedHtml);
  };

  // Custom Variable Dialog Component
  const CustomVariableDialog = () => {
    const [open, setOpen] = useState(false);
    const [name, setName] = useState("");
    const [description, setDescription] = useState("");
    const [defaultValue, setDefaultValue] = useState("");

    const handleAdd = () => {
      if (name) {
        handleAddCustomVariable(name, description, defaultValue);
        setName("");
        setDescription("");
        setDefaultValue("");
        setOpen(false);
      }
    };

    return (
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="w-full">
            <Plus className="h-4 w-4 mr-2" />
            Add Custom Variable
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add Custom Variable</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="varName" className="text-right">
                Name
              </Label>
              <Input
                id="varName"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder="variableName"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="varDescription" className="text-right">
                Description
              </Label>
              <Input
                id="varDescription"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                placeholder="What this variable represents"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="varDefault" className="text-right">
                Default Value
              </Label>
              <Input
                id="varDefault"
                value={defaultValue}
                onChange={(e) => setDefaultValue(e.target.value)}
                className="col-span-3"
                placeholder="Default value for preview"
              />
            </div>
          </div>
          <DialogFooter>
            <Button onClick={handleAdd}>Add Variable</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    );
  };

  return (
    <div className={className}>
      <div className="flex flex-col gap-6">
        <div className="flex flex-col lg:flex-row gap-6">
          <div className="w-full lg:w-2/3">
            <div className="mb-4 flex items-center justify-between bg-gray-50 p-3 rounded-md border border-gray-200">
              <div className="flex items-center space-x-3">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleUndo}
                        disabled={historyIndex <= 0}
                        className="h-10 w-10 rounded-full"
                      >
                        <Undo className="h-5 w-5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Undo</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleRedo}
                        disabled={historyIndex >= history.length - 1}
                        className="h-10 w-10 rounded-full"
                      >
                        <Redo className="h-5 w-5" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Redo</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowInlinePreview(!showInlinePreview)}
                  className="bg-white border-gray-200 hover:bg-gray-100 h-10 px-4"
                >
                  <Eye className="h-5 w-5 mr-2" />
                  {showInlinePreview ? "Hide Preview" : "Show Preview"}
                </Button>

                <Button
                  variant="default"
                  size="sm"
                  onClick={handleSave}
                  className="bg-orange-500 hover:bg-orange-600 h-10 px-4"
                >
                  <Save className="h-5 w-5 mr-2" />
                  Save Template
                </Button>
              </div>
            </div>

            <HtmlEditor
              value={html}
              onChange={setHtml}
              className="min-h-[600px]"
              placeholder="Start designing your email template..."
            />
          </div>

          <div className="w-full lg:w-1/3">
            <Tabs defaultValue="blocks" className="h-full">
              <TabsList className="w-full">
                <TabsTrigger value="blocks" className="flex-1 text-base py-2">
                  Blocks
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex-1 text-base py-2">
                  Settings
                </TabsTrigger>
                <TabsTrigger
                  value="variables"
                  className="flex-1 text-base py-2"
                >
                  Variables
                </TabsTrigger>
              </TabsList>

              <TabsContent value="blocks" className="p-4">
                <div className="mb-4">
                  <h3 className="text-base font-medium mb-2 text-gray-700">
                    Content Blocks
                  </h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Click on any block to add it to your template. Blocks will
                    be added at the end of your content.
                  </p>
                  <div className="bg-gray-50 p-4 rounded-md border border-gray-100">
                    <TemplateBlocksGrid
                      onSelectBlock={(block) => {
                        handleAddBlock(block);
                        toast({
                          title: "Block added",
                          description: `${block.name} has been added to your template.`,
                          duration: 2000,
                        });
                      }}
                    />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="settings" className="p-2">
                <Accordion type="single" collapsible className="w-full">
                  <AccordionItem value="general">
                    <AccordionTrigger>General Settings</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="contentWidth" className="text-xs">
                            Width (px)
                          </Label>
                          <Input
                            id="contentWidth"
                            type="number"
                            value={settings.contentWidth}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                contentWidth: parseInt(e.target.value),
                              })
                            }
                            className="col-span-2 h-8"
                          />
                        </div>

                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="bgColor" className="text-xs">
                            Background
                          </Label>
                          <div className="col-span-2 flex items-center gap-2">
                            <Input
                              id="bgColor"
                              type="color"
                              value={settings.backgroundColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  backgroundColor: e.target.value,
                                })
                              }
                              className="w-10 h-8 p-0"
                            />
                            <Input
                              value={settings.backgroundColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  backgroundColor: e.target.value,
                                })
                              }
                              className="h-8 flex-1"
                            />
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="typography">
                    <AccordionTrigger>Typography</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="fontFamily" className="text-xs">
                            Font Family
                          </Label>
                          <Select
                            value={settings.fontFamily}
                            onValueChange={(value) =>
                              setSettings({ ...settings, fontFamily: value })
                            }
                          >
                            <SelectTrigger className="col-span-2 h-8">
                              <SelectValue placeholder="Select font" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Arial, sans-serif">
                                Arial
                              </SelectItem>
                              <SelectItem value="'Helvetica Neue', Helvetica, sans-serif">
                                Helvetica
                              </SelectItem>
                              <SelectItem value="Georgia, serif">
                                Georgia
                              </SelectItem>
                              <SelectItem value="'Times New Roman', Times, serif">
                                Times New Roman
                              </SelectItem>
                              <SelectItem value="Verdana, sans-serif">
                                Verdana
                              </SelectItem>
                              <SelectItem value="Tahoma, sans-serif">
                                Tahoma
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="fontSize" className="text-xs">
                            Font Size (px)
                          </Label>
                          <Input
                            id="fontSize"
                            type="number"
                            value={settings.fontSize}
                            onChange={(e) =>
                              setSettings({
                                ...settings,
                                fontSize: parseInt(e.target.value),
                              })
                            }
                            className="col-span-2 h-8"
                          />
                        </div>

                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="textColor" className="text-xs">
                            Text Color
                          </Label>
                          <div className="col-span-2 flex items-center gap-2">
                            <Input
                              id="textColor"
                              type="color"
                              value={settings.textColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  textColor: e.target.value,
                                })
                              }
                              className="w-10 h-8 p-0"
                            />
                            <Input
                              value={settings.textColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  textColor: e.target.value,
                                })
                              }
                              className="h-8 flex-1"
                            />
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>

                  <AccordionItem value="buttons">
                    <AccordionTrigger>Buttons & Links</AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="linkColor" className="text-xs">
                            Link Color
                          </Label>
                          <div className="col-span-2 flex items-center gap-2">
                            <Input
                              id="linkColor"
                              type="color"
                              value={settings.linkColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  linkColor: e.target.value,
                                })
                              }
                              className="w-10 h-8 p-0"
                            />
                            <Input
                              value={settings.linkColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  linkColor: e.target.value,
                                })
                              }
                              className="h-8 flex-1"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="buttonColor" className="text-xs">
                            Button Color
                          </Label>
                          <div className="col-span-2 flex items-center gap-2">
                            <Input
                              id="buttonColor"
                              type="color"
                              value={settings.buttonColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  buttonColor: e.target.value,
                                })
                              }
                              className="w-10 h-8 p-0"
                            />
                            <Input
                              value={settings.buttonColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  buttonColor: e.target.value,
                                })
                              }
                              className="h-8 flex-1"
                            />
                          </div>
                        </div>

                        <div className="grid grid-cols-3 items-center gap-2">
                          <Label htmlFor="buttonTextColor" className="text-xs">
                            Button Text
                          </Label>
                          <div className="col-span-2 flex items-center gap-2">
                            <Input
                              id="buttonTextColor"
                              type="color"
                              value={settings.buttonTextColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  buttonTextColor: e.target.value,
                                })
                              }
                              className="w-10 h-8 p-0"
                            />
                            <Input
                              value={settings.buttonTextColor}
                              onChange={(e) =>
                                setSettings({
                                  ...settings,
                                  buttonTextColor: e.target.value,
                                })
                              }
                              className="h-8 flex-1"
                            />
                          </div>
                        </div>
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                </Accordion>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full mt-4"
                  onClick={applySettings}
                >
                  <Wand2 className="h-4 w-4 mr-2" />
                  Apply Settings
                </Button>
              </TabsContent>

              <TabsContent value="variables" className="p-4">
                <div className="space-y-5">
                  <div>
                    <h3 className="text-base font-medium mb-2 text-gray-700">
                      Standard Variables
                    </h3>
                    <p className="text-sm text-muted-foreground mb-3">
                      Click on a variable to copy it to clipboard. Use these
                      variables to personalize your email.
                    </p>
                    <div className="space-y-3 bg-gray-50 p-4 rounded-md border border-gray-100">
                      {variables.map((variable) => (
                        <div
                          key={variable.name}
                          className="flex items-center justify-between p-3 bg-white rounded-md border border-gray-200 hover:border-orange-200 hover:shadow-sm transition-colors"
                        >
                          <div>
                            <div className="font-medium text-sm text-orange-600">
                              {`{{${variable.name}}}`}
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              {variable.description}
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-9 px-3"
                            onClick={() => {
                              navigator.clipboard.writeText(
                                `{{${variable.name}}}`
                              );
                              toast({
                                title: "Copied to clipboard",
                                description: `Variable {{${variable.name}}} copied to clipboard.`,
                                duration: 2000,
                              });
                            }}
                          >
                            <Copy className="h-4 w-4 mr-2" /> Copy
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>

                  {customVariables.length > 0 && (
                    <div>
                      <h3 className="text-base font-medium mb-2 text-gray-700">
                        Custom Variables
                      </h3>
                      <div className="space-y-3 bg-gray-50 p-4 rounded-md border border-gray-100">
                        {customVariables.map((variable) => (
                          <div
                            key={variable.name}
                            className="flex items-center justify-between p-3 bg-white rounded-md border border-gray-200 hover:border-orange-200 hover:shadow-sm transition-colors"
                          >
                            <div>
                              <div className="font-medium text-sm text-orange-600">
                                {`{{${variable.name}}}`}
                              </div>
                              <div className="text-sm text-muted-foreground mt-1">
                                {variable.description}
                              </div>
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-9 px-3"
                                onClick={() => {
                                  navigator.clipboard.writeText(
                                    `{{${variable.name}}}`
                                  );
                                  toast({
                                    title: "Copied to clipboard",
                                    description: `Variable {{${variable.name}}} copied to clipboard.`,
                                    duration: 2000,
                                  });
                                }}
                              >
                                <Copy className="h-4 w-4 mr-2" /> Copy
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-9 px-3"
                                onClick={() => {
                                  setCustomVariables(
                                    customVariables.filter(
                                      (v) => v.name !== variable.name
                                    )
                                  );
                                  toast({
                                    title: "Variable removed",
                                    description: `Variable {{${variable.name}}} has been removed.`,
                                    duration: 2000,
                                  });
                                }}
                              >
                                <Trash2 className="h-4 w-4 mr-2" /> Remove
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="pt-2">
                    <CustomVariableDialog />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Inline Preview */}
        {showInlinePreview && (
          <div className="mt-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-xl font-medium text-gray-800 flex items-center">
                <Eye className="mr-3 h-6 w-6 text-orange-500" />
                Email Preview
              </h3>
              <Button
                variant="outline"
                size="sm"
                onClick={handlePreview}
                className="bg-white border-gray-200 hover:bg-gray-100 h-10 px-4"
              >
                <Maximize className="h-5 w-5 mr-2" />
                Full Screen
              </Button>
            </div>
            <div className="border rounded-md p-6 bg-white shadow-sm">
              <div
                dangerouslySetInnerHTML={{ __html: html }}
                className="max-h-[600px] overflow-auto"
              />
            </div>
          </div>
        )}
      </div>

      {/* Preview Dialog */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-[90vw] max-h-[90vh] overflow-auto">
          <DialogHeader>
            <DialogTitle className="text-xl flex items-center">
              <Eye className="mr-3 h-6 w-6 text-orange-500" />
              Email Preview
            </DialogTitle>
            <DialogDescription>
              This is how your email will appear to recipients
            </DialogDescription>
          </DialogHeader>
          <div
            className="mt-4 border rounded-md p-6 overflow-auto bg-white"
            dangerouslySetInnerHTML={{ __html: previewHtml }}
          />
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowPreview(false)}
              className="mt-4 h-10"
            >
              <X className="h-5 w-5 mr-2" />
              Close Preview
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
