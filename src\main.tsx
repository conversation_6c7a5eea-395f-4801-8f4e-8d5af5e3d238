import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { Toaster } from "sonner";
import "./index.css";
import "react-quill/dist/quill.snow.css";
import "./styles/quill-overrides.css"; // Import our custom Quill overrides
import App from "./App.tsx";
import { ReactQueryProvider } from "./lib/react-query/ReactQueryProvider";
import { registerServiceWorker } from "./utils/serviceWorkerRegistration";

// Register service worker for offline support
registerServiceWorker().catch((error) => {
  console.error("Service worker registration failed:", error);
});

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ReactQueryProvider>
      <Toaster position="top-center" richColors />
      <App />
    </ReactQueryProvider>
  </StrictMode>
);
