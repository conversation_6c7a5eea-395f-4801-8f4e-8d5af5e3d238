# Secure Authentication System Documentation

This document outlines the secure authentication system implemented between the Qonai frontend and the Newsletter API backend.

## Overview

The authentication system uses a multi-layered approach:

1. **JWT-based authentication** for secure token exchange
2. **API key management** for service-to-service communication
3. **Rate limiting** to prevent abuse
4. **Request validation and sanitization** to prevent injection attacks
5. **Comprehensive logging** for security monitoring
6. **CORS configuration** for secure cross-origin requests

## Frontend Implementation (Qonai)

### ApiAuthService.ts

The `ApiAuthService.ts` file provides a secure way to authenticate with the Newsletter API:

```typescript
// src/services/ApiAuthService.ts
import { auth } from "@/config/firebase";

// Environment variables
const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL || 'https://api.qonai.me';
const NEWSLETTER_API_KEY = import.meta.env.VITE_APP_NEWSLETTER_API_KEY;

// Generate API token (either Firebase ID token or API key)
const generateApiToken = async (): Promise<string> => {
  // If user is logged in, use Firebase ID token
  const currentUser = auth.currentUser;
  if (currentUser) {
    return await currentUser.getIdToken(true);
  }
  
  // Otherwise use API key
  if (NEWSLETTER_API_KEY) {
    return NEWSLETTER_API_KEY;
  }
  
  throw new Error('No authentication method available');
};

// Make authenticated API request with retry logic
export const apiRequest = async <T>(
  url: string,
  options: ApiRequestOptions = {},
  authOptions: ApiAuthOptions = {}
): Promise<T> => {
  // Implementation details...
};
```

### NotificationService.ts

The `NotificationService.ts` file uses the `ApiAuthService` to securely send email notifications:

```typescript
// src/services/NotificationService.ts
import { apiRequest } from "./ApiAuthService";

// Constants
const API_BASE_URL = import.meta.env.VITE_APP_API_BASE_URL || 'https://api.qonai.me';
const EMAIL_ENDPOINT = '/api/send-notification-email';
const REQUEST_TIMEOUT = 15000; // 15 seconds
const MAX_RETRIES = 3;

// Send email notification with secure authentication and retry logic
sendEmailNotification: async (options: EmailOptions): Promise<boolean> => {
  try {
    // Validate required fields
    if (!options.to || !options.subject) {
      console.error("Email notification missing required fields:", options);
      return false;
    }

    // Use the secure API request method with retry logic
    const response = await apiRequest<EmailResponse>(
      EMAIL_ENDPOINT,
      {
        method: "POST",
        body: JSON.stringify(options),
        timeout: REQUEST_TIMEOUT,
      },
      {
        maxRetries: MAX_RETRIES,
        retryDelay: 1000,
      }
    );
    
    return response.success;
  } catch (error: any) {
    // Detailed error handling...
    return false;
  }
}
```

## Backend Implementation (Newsletter API)

### authService.js

The `authService.js` file provides JWT token generation and API key management:

```javascript
// services/authService.js
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { db } = require('../config/firebase');

// JWT configuration
const JWT_SECRET = process.env.JWT_SECRET || crypto.randomBytes(64).toString('hex');
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

// Generate JWT token
const generateToken = (payload, expiresIn = JWT_EXPIRES_IN) => {
  return jwt.sign(payload, JWT_SECRET, { expiresIn });
};

// Verify JWT token
const verifyToken = (token) => {
  return jwt.verify(token, JWT_SECRET);
};

// Generate API key
const generateApiKey = async (clientId, name, scope = 'read') => {
  // Implementation details...
};

// Validate API key
const validateApiKey = async (apiKey) => {
  // Implementation details...
};
```

### authMiddleware.js

The `authMiddleware.js` file provides middleware for verifying tokens and API keys:

```javascript
// middlewares/authMiddleware.js
const { admin } = require("../config/firebase");
const authService = require("../services/authService");
const logger = require("../utils/logger");

// Verify Firebase ID token or JWT token
const verifyToken = async (req, res, next) => {
  // Implementation details...
};

// Verify API key
const verifyApiKey = async (req, res, next) => {
  // Implementation details...
};
```

### rateLimitMiddleware.js

The `rateLimitMiddleware.js` file provides rate limiting for different endpoints:

```javascript
// middlewares/rateLimitMiddleware.js
const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');

// General API rate limiter
const apiLimiter = createRateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // 100 requests per 15 minutes
});

// Email rate limiter
const emailLimiter = createRateLimiter({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // 20 emails per hour
});

// Subscription rate limiter
const subscriptionLimiter = createRateLimiter({
  windowMs: 24 * 60 * 60 * 1000, // 24 hours
  max: 5, // 5 subscription attempts per 24 hours
});
```

## Security Best Practices

1. **No hardcoded credentials**: All sensitive information is stored in environment variables
2. **HTTPS enforcement**: All API requests use HTTPS
3. **Input validation and sanitization**: All user input is validated and sanitized
4. **Rate limiting**: Prevents abuse of the API
5. **Comprehensive logging**: All security events are logged
6. **IP tracking**: Suspicious activity is tracked by IP address
7. **CORS configuration**: Only allows requests from trusted origins

## Environment Variables

### Frontend (Qonai)

```
# API Configuration
VITE_APP_API_BASE_URL=https://api.qonai.me
VITE_APP_NEWSLETTER_API_KEY=your_newsletter_api_key
```

### Backend (Newsletter API)

```
# JWT Configuration
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h

# API Keys
API_KEY=your_api_key
LEGACY_API_KEY=your_legacy_api_key
```

## Maintenance and Troubleshooting

### Common Issues

1. **401 Unauthorized**: Check that the API key is correctly set in the environment variables
2. **429 Too Many Requests**: Rate limit exceeded, wait and try again later
3. **400 Bad Request**: Invalid request data, check the request payload

### Monitoring

1. **Check logs**: All security events are logged
2. **Monitor rate limits**: Watch for suspicious activity
3. **Rotate API keys**: Regularly rotate API keys for better security

## Future Improvements

1. **Implement refresh tokens**: For longer sessions without compromising security
2. **Add two-factor authentication**: For critical operations
3. **Implement IP-based blocking**: For repeated suspicious activity
4. **Add anomaly detection**: To identify unusual patterns of activity
