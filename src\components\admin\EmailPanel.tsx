import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { EmailTemplateEditor } from "@/components/ui/email-template-editor";
import {
  Loader2,
  Plus,
  Trash,
  Edit,
  Check,
  X,
  Eye,
  Send,
  Maximize,
} from "lucide-react";
import { useAuth } from "@/providers/AuthProvider";
import {
  collection,
  addDoc,
  query,
  orderBy,
  onSnapshot,
  Timestamp,
  doc,
  updateDoc,
} from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { format } from "date-fns";

// Helper function to safely format dates
const formatFirestoreDate = (
  timestamp: Timestamp | Record<string, number> | number | string | undefined
): string => {
  try {
    // Check if timestamp has _seconds property (Firestore Timestamp from server)
    if (timestamp && typeof timestamp === "object" && "_seconds" in timestamp) {
      const date = new Date(timestamp._seconds * 1000);
      // Check if date is valid
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }
    }

    // Check if timestamp has seconds property (Firestore Timestamp)
    if (timestamp && typeof timestamp === "object" && "seconds" in timestamp) {
      const date = new Date(timestamp.seconds * 1000);
      // Check if date is valid
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }
    }

    // Check if timestamp is a string that might be a date
    if (timestamp && typeof timestamp === "string") {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }

      // Special case for the timestamp format in the screenshot (May 10, 2025 at 4:24:21 AM UTC+4)
      if (timestamp.includes("at")) {
        try {
          // Extract the date part before "at"
          const datePart = timestamp.split("at")[0].trim();
          const date = new Date(datePart);
          if (!isNaN(date.getTime())) {
            return format(date, "MMM d, yyyy");
          }
        } catch {
          // Continue with other checks
        }
      }
    }

    // If timestamp is a number (unix timestamp)
    if (timestamp && typeof timestamp === "number") {
      const date = new Date(timestamp);
      if (!isNaN(date.getTime())) {
        return format(date, "MMM d, yyyy");
      }
    }

    return "Unknown date";
  } catch (error) {
    console.error("Error formatting date:", error, timestamp);
    return "Unknown date";
  }
};

// Email template interface
interface EmailTemplate {
  id: string;
  subject: string;
  content: string;
  createdAt: Timestamp;
  category?: string;
  description?: string;
  variables?: string[];
  lastUsed?: Timestamp;
  usageCount?: number;
}

// Subscriber interface
interface Subscriber {
  id: string;
  email: string;
  name?: string;
  subscribed: boolean;
  subscribedAt?: {
    seconds: number;
    nanoseconds: number;
  };
  createdAt?: Timestamp | Record<string, number> | number; // Could be Timestamp or other format
  timestamp?: string; // For the format "May 10, 2025 at 4:24:21 AM UTC+4"
}

// Predefined HTML templates
const predefinedTemplates = [
  {
    name: "Welcome Template",
    category: "Onboarding",
    subject: "Welcome to Our Newsletter!",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center;">Welcome, {{name}}!</h1>
          <p style="color: #666666; line-height: 1.6;">
            Thank you for subscribing to our newsletter! We're excited to share the latest restaurant recommendations and food trends with you.
          </p>
          <p style="color: #666666; line-height: 1.6;">
            Stay tuned for delicious updates!
          </p>
          <div style="text-align: center; margin-top: 20px;">
            <a href="https://qonai.me" style="background-color: #ff6200; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Explore Now
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 20px;">
            You received this email because you subscribed to our newsletter.
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a>
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Weekly Update Template",
    category: "Newsletter",
    subject: "Weekly Food Trends Update",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center;">Weekly Food Trends</h1>
          <p style="color: #666666; line-height: 1.6;">
            Hi {{name}}, here are this week's top food trends and restaurant recommendations!
          </p>
          <ul style="color: #666666; line-height: 1.6;">
            <li>Trend 1: Plant-based dishes are on the rise!</li>
            <li>Trend 2: Fusion cuisine is making a comeback.</li>
          </ul>
          <div style="text-align: center; margin-top: 20px;">
            <a href="https://qonai.me" style="background-color: #ff6200; color: #ffffff; padding: 10px 20px; text-decoration: none; border-radius: 5px;">
              Discover More
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 20px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a>
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Special Offer",
    category: "Promotions",
    subject: "Limited Time Offer Just For You!",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://qonai.me/images/special-offer.png" alt="Special Offer" style="max-width: 150px;">
          </div>
          <h1 style="color: #d23600; text-align: center; margin-bottom: 20px;">Special Offer Inside!</h1>
          <p style="color: #666666; line-height: 1.6;">
            Hello {{name}},
          </p>
          <p style="color: #666666; line-height: 1.6;">
            We're excited to offer you an exclusive deal at our partner restaurants this week!
          </p>
          <div style="background-color: #fff4e6; border-left: 4px solid #ff6200; padding: 15px; margin: 20px 0;">
            <h3 style="color: #333333; margin-top: 0;">{{offerTitle}}</h3>
            <p style="color: #666666; margin-bottom: 0;">{{offerDescription}}</p>
            <p style="font-weight: bold; color: #d23600; margin-top: 10px;">Valid until: {{offerExpiry}}</p>
          </div>
          <div style="text-align: center; margin-top: 25px;">
            <a href="{{offerLink}}" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              Claim Your Offer
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from promotional emails.
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "New Restaurant Announcement",
    category: "Announcements",
    subject: "New Restaurant Alert: {{restaurantName}} Just Joined Qonai!",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center;">New Restaurant Alert!</h1>
          <p style="color: #666666; line-height: 1.6;">
            Hello {{name}},
          </p>
          <p style="color: #666666; line-height: 1.6;">
            We're excited to announce that <strong>{{restaurantName}}</strong> has just joined the Qonai platform!
          </p>
          <div style="margin: 25px 0; text-align: center;">
            <img src="{{restaurantImage}}" alt="{{restaurantName}}" style="max-width: 100%; border-radius: 8px; max-height: 250px; object-fit: cover;">
          </div>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333333; margin-top: 0;">About {{restaurantName}}</h3>
            <p style="color: #666666; line-height: 1.6;">{{restaurantDescription}}</p>
            <p style="color: #666666;"><strong>Cuisine:</strong> {{restaurantCuisine}}</p>
            <p style="color: #666666;"><strong>Location:</strong> {{restaurantLocation}}</p>
          </div>
          <div style="text-align: center; margin-top: 25px;">
            <a href="{{restaurantLink}}" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              View Menu & Order Now
            </a>
          </div>
          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from our newsletter.
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Monthly Digest",
    category: "Newsletter",
    subject: "Your Monthly Food Digest - {{month}} Highlights",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <h1 style="color: #333333; text-align: center; margin-bottom: 5px;">{{month}} Food Digest</h1>
          <p style="color: #666666; text-align: center; margin-top: 0;">Your monthly roundup of food discoveries</p>

          <p style="color: #666666; line-height: 1.6; margin-top: 20px;">
            Hello {{name}},
          </p>
          <p style="color: #666666; line-height: 1.6;">
            Here's your personalized monthly digest of food trends, new restaurants, and special offers based on your preferences.
          </p>

          <div style="margin: 25px 0;">
            <h2 style="color: #ff6200; border-bottom: 2px solid #ff6200; padding-bottom: 8px;">Top Picks For You</h2>
            <div style="display: flex; gap: 15px; margin-top: 15px;">
              <div style="flex: 1; background-color: #f5f5f5; border-radius: 8px; padding: 15px; text-align: center;">
                <img src="{{topPick1Image}}" alt="{{topPick1Name}}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 5px;">
                <h3 style="color: #333333; margin: 10px 0 5px;">{{topPick1Name}}</h3>
                <p style="color: #666666; font-size: 14px; margin: 0;">{{topPick1Description}}</p>
              </div>
              <div style="flex: 1; background-color: #f5f5f5; border-radius: 8px; padding: 15px; text-align: center;">
                <img src="{{topPick2Image}}" alt="{{topPick2Name}}" style="width: 100%; height: 120px; object-fit: cover; border-radius: 5px;">
                <h3 style="color: #333333; margin: 10px 0 5px;">{{topPick2Name}}</h3>
                <p style="color: #666666; font-size: 14px; margin: 0;">{{topPick2Description}}</p>
              </div>
            </div>
          </div>

          <div style="margin: 30px 0;">
            <h2 style="color: #ff6200; border-bottom: 2px solid #ff6200; padding-bottom: 8px;">This Month's Food Trend</h2>
            <div style="margin-top: 15px;">
              <h3 style="color: #333333; margin: 0 0 10px;">{{trendTitle}}</h3>
              <p style="color: #666666; line-height: 1.6;">{{trendDescription}}</p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 30px;">
            <a href="https://qonai.me/explore" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              Explore More Restaurants
            </a>
          </div>

          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from our newsletter.
          </p>
        </div>
      </div>
    `,
  },
  {
    name: "Loyalty Reward Notification",
    category: "Loyalty",
    subject: "You've Earned a New Reward! 🎁",
    content: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://qonai.me/images/reward-badge.png" alt="Reward Badge" style="max-width: 120px;">
          </div>
          <h1 style="color: #333333; text-align: center; margin-bottom: 20px;">Congratulations, {{name}}!</h1>

          <div style="background-color: #fff8e6; border-radius: 8px; padding: 20px; margin: 20px 0; text-align: center; border: 2px dashed #ffb74d;">
            <h2 style="color: #ff6200; margin-top: 0;">You've Earned a New Reward!</h2>
            <p style="color: #666666; font-size: 18px; margin-bottom: 5px;">{{rewardTitle}}</p>
            <p style="color: #666666; margin-bottom: 15px;">{{rewardDescription}}</p>
            <div style="background-color: #ffffff; display: inline-block; padding: 10px 20px; border-radius: 4px; font-size: 18px; font-weight: bold; letter-spacing: 2px; color: #333333; border: 1px solid #ddd;">{{rewardCode}}</div>
          </div>

          <div style="color: #666666; line-height: 1.6;">
            <p>Your loyalty has been rewarded! You've reached {{pointsEarned}} points and unlocked this special reward.</p>
            <p>Current loyalty level: <strong>{{loyaltyLevel}}</strong></p>
            <p>Total points balance: <strong>{{pointsBalance}}</strong></p>
          </div>

          <div style="margin: 25px 0; padding: 15px; background-color: #f5f5f5; border-radius: 8px;">
            <h3 style="color: #333333; margin-top: 0;">How to Redeem Your Reward:</h3>
            <ol style="color: #666666; padding-left: 20px;">
              <li>Visit any participating restaurant</li>
              <li>Show this email or enter your reward code at checkout</li>
              <li>Enjoy your reward!</li>
            </ol>
            <p style="color: #666666; font-style: italic; margin-bottom: 0;">Valid until: {{expiryDate}}</p>
          </div>

          <div style="text-align: center; margin-top: 25px;">
            <a href="https://qonai.me/rewards" style="background-color: #ff6200; color: #ffffff; padding: 12px 25px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
              View All Your Rewards
            </a>
          </div>

          <p style="color: #999999; font-size: 12px; text-align: center; margin-top: 30px;">
            <a href="{{unsubscribeLink}}" style="color: #999999;">Unsubscribe</a> from promotional emails.
          </p>
        </div>
      </div>
    `,
  },
];

export function EmailPanel() {
  const { user } = useAuth();
  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [newTemplate, setNewTemplate] = useState({
    subject: "",
    content: "",
    category: "General",
    description: "",
    variables: [],
  });
  const [emailSubject, setEmailSubject] = useState("");
  const [emailContent, setEmailContent] = useState("");
  const [isSending, setIsSending] = useState(false);
  const [previewContent, setPreviewContent] = useState("");
  const [testEmail, setTestEmail] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");

  useEffect(() => {
    if (!user) return;

    const fetchSubscribers = async () => {
      try {
        // Show loading toast

        const apiUrl =
          process.env.NODE_ENV === "production"
            ? "https://api.qonai.me/subscribers"
            : "http://localhost:3000/subscribers";

        const response = await fetch(apiUrl, {
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          // Add credentials if your API requires authentication
          // credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(
            `Failed to fetch subscribers: ${response.status} ${response.statusText}`
          );
        }

        const data = await response.json();

        setSubscribers(data);
      } catch (error) {
        console.error("Error fetching subscribers:", error);
        setSubscribers([]);
      }
    };

    fetchSubscribers();
    // Reduce polling frequency to avoid unnecessary API calls
    const intervalId = setInterval(fetchSubscribers, 300000); // 5 minutes
    return () => clearInterval(intervalId);
  }, [user]);

  useEffect(() => {
    if (!user) return;

    const templatesRef = collection(firestore, "emailTemplates");
    const q = query(templatesRef, orderBy("createdAt", "desc"));

    const unsubscribe = onSnapshot(q, (snapshot) => {
      const templatesList: EmailTemplate[] = [];
      snapshot.forEach((doc) =>
        templatesList.push({ id: doc.id, ...doc.data() } as EmailTemplate)
      );
      setTemplates(templatesList);
    });

    return () => unsubscribe();
  }, [user]);

  if (!user || user.email !== "<EMAIL>") {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-6 text-center">
        <h2 className="text-2xl font-bold text-gray-900">Access Denied</h2>
        <p className="mt-2 text-gray-600">
          You don't have permission to access this page.
        </p>
      </div>
    );
  }

  const saveTemplate = async () => {
    try {
      // Extract variables from content using regex
      const variableMatches =
        newTemplate.content.match(/\{\{([^}]+)\}\}/g) || [];
      const variables = variableMatches.map((match) =>
        match.replace(/\{\{|\}\}/g, "")
      );

      // Remove duplicates from variables array
      const uniqueVariables = [...new Set(variables)];

      const templatesRef = collection(firestore, "emailTemplates");
      await addDoc(templatesRef, {
        ...newTemplate,
        variables: uniqueVariables,
        createdAt: Timestamp.now(),
        usageCount: 0,
      });
      toast.success("Template saved successfully");
      setNewTemplate({
        subject: "",
        content: "",
        category: "General",
        description: "",
        variables: [],
      });
    } catch (error) {
      console.error("Error saving template:", error);
      toast.error("Failed to save template");
    }
  };

  const sendNewsletter = async () => {
    if (!emailSubject || !emailContent) {
      toast.error("Please fill in both subject and content");
      return;
    }

    // Check if we have subscribers
    if (subscribers.length === 0) {
      toast.error("No subscribers found to send newsletter to");
      return;
    }

    // Get active subscribers
    const activeSubscribers = subscribers.map((sub) => sub.email);

    if (activeSubscribers.length === 0) {
      toast.error("No active subscribers found");
      return;
    }

    setIsSending(true);
    try {
      // Try the newsletter API endpoint first
      const apiUrl =
        process.env.NODE_ENV === "production"
          ? "https://api.qonai.me/send-newsletter"
          : "http://localhost:3000/send-newsletter";

      // Log the request for debugging
      console.log("Sending newsletter to:", {
        subscriberCount: activeSubscribers.length,
        firstFew: activeSubscribers.slice(0, 3),
        subject:
          emailSubject.substring(0, 30) +
          (emailSubject.length > 30 ? "..." : ""),
      });

      // Prepare the request payload
      const requestData = {
        subject: emailSubject,
        content: emailContent,
        recipients: activeSubscribers, // Send the list of active subscribers
        emails: activeSubscribers, // Alternative field name
        to: activeSubscribers, // Another alternative field name
        subscribed: true, // Explicitly indicate these are active subscribers
      };

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(requestData),
      });

      // Handle non-OK responses
      if (!response.ok) {
        let errorMessage = `Server returned ${response.status}: ${response.statusText}`;

        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          // If we can't parse the error as JSON, use the status text
          console.error("Error parsing error response:", jsonError);
        }

        throw new Error(errorMessage);
      }

      // Parse the successful response
      let result;
      try {
        result = await response.json();
      } catch (jsonError) {
        // If we can't parse the response as JSON, create a default result
        console.warn("Could not parse response as JSON:", jsonError);
        result = { success: true, recipientCount: activeSubscribers.length };
      }

      // Show success message
      toast.success(
        `Newsletter sent to ${
          result.recipientCount || activeSubscribers.length
        } subscribers`
      );

      // Clear the form
      setEmailSubject("");
      setEmailContent("");
      setPreviewContent("");
    } catch (error) {
      console.error("Error sending newsletter:", error);

      // Try sending test email as fallback
      if (activeSubscribers.length > 0) {
        try {
          toast.info("Attempting to send to first subscriber as test...");
          await sendSingleEmail(activeSubscribers[0]);
        } catch (fallbackError) {
          console.error("Fallback also failed:", fallbackError);
        }
      }

      toast.error(
        error instanceof Error ? error.message : "Failed to send newsletter"
      );
    } finally {
      setIsSending(false);
    }
  };

  // Helper function to send to a single email
  const sendSingleEmail = async (email: string) => {
    const apiUrl =
      process.env.NODE_ENV === "production"
        ? "https://api.qonai.me/send-notification-email"
        : "http://localhost:3000/send-notification-email";

    // Find subscriber info if available
    const subscriber = subscribers.find((sub) => sub.email === email);
    const subscriberName = subscriber?.name || "there";

    // Replace template variables with actual values
    const personalizedContent = emailContent
      .replace(/\{\{name\}\}/g, subscriberName)
      .replace(/\{\{unsubscribeLink\}\}/g, "#");

    const response = await fetch(apiUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Accept: "application/json",
      },
      body: JSON.stringify({
        to: email,
        subject: emailSubject,
        body: personalizedContent,
        type: "marketing",
        recipientName: subscriberName,
      }),
    });

    if (!response.ok) {
      throw new Error(
        `Failed to send to ${email}: ${response.status} ${response.statusText}`
      );
    }

    toast.success(`Sent newsletter to ${email}`);
    return await response.json();
  };

  const sendTestEmail = async () => {
    if (!emailSubject || !emailContent || !testEmail) {
      toast.error("Please fill in subject, content, and test email address");
      return;
    }

    setIsSending(true);
    try {
      // First try the newsletter API with test flag
      try {
        const apiUrl =
          process.env.NODE_ENV === "production"
            ? "https://api.qonai.me/send-newsletter"
            : "http://localhost:3000/send-newsletter";

        const response = await fetch(apiUrl, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Accept: "application/json",
          },
          body: JSON.stringify({
            subject: emailSubject,
            content: emailContent
              .replace(/\{\{name\}\}/g, "Test User")
              .replace(/\{\{unsubscribeLink\}\}/g, "#"),
            testEmail, // Test email parameter
            isTest: true, // Flag to indicate this is a test email
            recipients: [testEmail], // Also include in recipients array
            emails: [testEmail], // Alternative field name
            to: [testEmail], // Another alternative field name
            recipientName: "Test User", // Add recipient name for template variables
          }),
        });

        if (response.ok) {
          toast.success(`Test email sent to ${testEmail}`);
          return;
        }

        // If newsletter API fails, throw error to try fallback
        throw new Error(
          `Newsletter API returned ${response.status}: ${response.statusText}`
        );
      } catch (newsletterError) {
        console.warn("Newsletter API failed for test email:", newsletterError);

        // Fall back to notification email API
        await sendSingleEmail(testEmail);
      }
    } catch (error) {
      console.error("Error sending test email:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to send test email"
      );
    } finally {
      setIsSending(false);
    }
  };

  const handleTemplateSelect = (template: {
    name?: string;
    subject: string;
    content: string;
    category?: string;
    variables?: string[];
  }) => {
    setEmailSubject(template.subject);
    setEmailContent(template.content);

    // Create a preview with sample data for all variables
    let previewContent = template.content;

    // Replace common variables first
    previewContent = previewContent
      .replace(/\{\{name\}\}/g, "Test User")
      .replace(/\{\{unsubscribeLink\}\}/g, "#");

    // Replace any other variables with sample data
    const variableMatches = template.content.match(/\{\{([^}]+)\}\}/g) || [];
    variableMatches.forEach((variable) => {
      const variableName = variable.replace(/\{\{|\}\}/g, "");
      if (variableName !== "name" && variableName !== "unsubscribeLink") {
        // Generate sample data based on variable name
        let sampleValue = "Sample Value";

        // Special cases for common variable types
        if (
          variableName.toLowerCase().includes("date") ||
          variableName.toLowerCase().includes("expiry")
        ) {
          sampleValue = "June 30, 2024";
        } else if (
          variableName.toLowerCase().includes("price") ||
          variableName.toLowerCase().includes("amount")
        ) {
          sampleValue = "$49.99";
        } else if (
          variableName.toLowerCase().includes("percent") ||
          variableName.toLowerCase().includes("discount")
        ) {
          sampleValue = "25%";
        } else if (variableName.toLowerCase().includes("restaurant")) {
          sampleValue = "Delicious Bistro";
        } else if (
          variableName.toLowerCase().includes("image") ||
          variableName.toLowerCase().includes("img")
        ) {
          sampleValue = "https://via.placeholder.com/300x200";
        } else if (
          variableName.toLowerCase().includes("link") ||
          variableName.toLowerCase().includes("url")
        ) {
          sampleValue = "#";
        } else if (variableName.toLowerCase().includes("description")) {
          sampleValue =
            "This is a sample description text that would appear in your email.";
        } else if (variableName.toLowerCase().includes("title")) {
          sampleValue = "Sample Title";
        } else if (variableName.toLowerCase().includes("month")) {
          sampleValue = "June";
        }

        // Replace all occurrences of this variable
        const regex = new RegExp(`\\{\\{${variableName}\\}\\}`, "g");
        previewContent = previewContent.replace(regex, sampleValue);
      }
    });

    setPreviewContent(previewContent);
  };

  const handlePreview = () => {
    // Create a preview with sample data for all variables
    let previewContent = emailContent;

    // Replace common variables first
    previewContent = previewContent
      .replace(/\{\{name\}\}/g, "Test User")
      .replace(/\{\{unsubscribeLink\}\}/g, "#");

    // Replace any other variables with sample data
    const variableMatches = emailContent.match(/\{\{([^}]+)\}\}/g) || [];
    variableMatches.forEach((variable) => {
      const variableName = variable.replace(/\{\{|\}\}/g, "");
      if (variableName !== "name" && variableName !== "unsubscribeLink") {
        // Generate sample data based on variable name
        let sampleValue = "Sample Value";

        // Special cases for common variable types
        if (
          variableName.toLowerCase().includes("date") ||
          variableName.toLowerCase().includes("expiry")
        ) {
          sampleValue = "June 30, 2024";
        } else if (
          variableName.toLowerCase().includes("price") ||
          variableName.toLowerCase().includes("amount")
        ) {
          sampleValue = "$49.99";
        } else if (
          variableName.toLowerCase().includes("percent") ||
          variableName.toLowerCase().includes("discount")
        ) {
          sampleValue = "25%";
        } else if (variableName.toLowerCase().includes("restaurant")) {
          sampleValue = "Delicious Bistro";
        } else if (
          variableName.toLowerCase().includes("image") ||
          variableName.toLowerCase().includes("img")
        ) {
          sampleValue = "https://via.placeholder.com/300x200";
        } else if (
          variableName.toLowerCase().includes("link") ||
          variableName.toLowerCase().includes("url")
        ) {
          sampleValue = "#";
        } else if (variableName.toLowerCase().includes("description")) {
          sampleValue =
            "This is a sample description text that would appear in your email.";
        } else if (variableName.toLowerCase().includes("title")) {
          sampleValue = "Sample Title";
        } else if (variableName.toLowerCase().includes("month")) {
          sampleValue = "June";
        }

        // Replace all occurrences of this variable
        const regex = new RegExp(`\\{\\{${variableName}\\}\\}`, "g");
        previewContent = previewContent.replace(regex, sampleValue);
      }
    });

    setPreviewContent(previewContent);
  };

  return (
    <section className="sm:p-2 lg:p-4">
      <Card className="">
        <CardContent className="p-6">
          <Tabs defaultValue="compose" className="w-full">
            <TabsList className="grid w-full grid-cols-3 bg-gray-100 rounded-lg p-1">
              <TabsTrigger value="compose" className="rounded-md">
                Compose
              </TabsTrigger>
              <TabsTrigger value="templates" className="rounded-md">
                Templates
              </TabsTrigger>
              <TabsTrigger value="subscribers" className="rounded-md">
                Subscribers
              </TabsTrigger>
            </TabsList>

            <TabsContent value="compose" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <Label
                      htmlFor="subject"
                      className="text-sm font-medium text-gray-700"
                    >
                      Subject
                    </Label>
                    <Input
                      id="subject"
                      value={emailSubject}
                      onChange={(e) => setEmailSubject(e.target.value)}
                      placeholder="Newsletter subject"
                      className="mt-1 h-12 text-base"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor="content"
                      className="text-sm font-medium text-gray-700"
                    >
                      Content
                    </Label>
                    <div className="mt-2">
                      <EmailTemplateEditor
                        initialHtml={emailContent}
                        onSave={(html) => {
                          setEmailContent(html);
                        }}
                        onPreview={(html) => {
                          setPreviewContent(html);
                        }}
                      />
                    </div>
                  </div>
                  <div className="flex gap-3">
                    <Button
                      onClick={handlePreview}
                      className="bg-orange-500 hover:bg-orange-600 h-10"
                    >
                      <Eye className="mr-2 h-5 w-5" />
                      Preview
                    </Button>
                    <Button
                      onClick={sendNewsletter}
                      disabled={isSending}
                      className="bg-orange-500 hover:bg-orange-600 h-10"
                    >
                      {isSending ? (
                        <>
                          <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                          Sending...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-5 w-5" />
                          Send Newsletter
                        </>
                      )}
                    </Button>
                  </div>
                  <div className="space-y-2">
                    <Label
                      htmlFor="testEmail"
                      className="text-sm font-medium text-gray-700"
                    >
                      Test Email
                    </Label>
                    <div className="flex gap-3">
                      <Input
                        id="testEmail"
                        value={testEmail}
                        onChange={(e) => setTestEmail(e.target.value)}
                        placeholder="Enter test email address"
                        className="h-12 text-base"
                      />
                      <Button
                        onClick={sendTestEmail}
                        disabled={isSending}
                        className="bg-orange-500 hover:bg-orange-600 h-10"
                      >
                        <Send className="mr-2 h-5 w-5" />
                        Send Test Email
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-gray-700">
                      Select Predefined Template
                    </Label>
                    <div className="flex gap-3 mt-1">
                      {predefinedTemplates.map((template) => (
                        <Button
                          key={template.name}
                          onClick={() => handleTemplateSelect(template)}
                          variant="outline"
                          className="border-gray-300 hover:bg-gray-100"
                        >
                          {template.name}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="hidden md:block">
                  {/* Preview will be shown below in mobile view */}
                </div>
              </div>

              {previewContent && (
                <div className="mt-8">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-medium text-gray-800 flex items-center">
                      <Eye className="mr-2 h-5 w-5 text-orange-500" />
                      Email Preview
                    </h3>
                  </div>
                  <div className="border rounded-md p-6 bg-white shadow-sm">
                    <div
                      dangerouslySetInnerHTML={{ __html: previewContent }}
                      className="max-h-[600px] overflow-auto"
                    />
                  </div>
                </div>
              )}
            </TabsContent>

            <TabsContent value="templates" className="mt-6">
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-800">
                    Create New Template
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label
                        htmlFor="templateSubject"
                        className="text-sm font-medium text-gray-700"
                      >
                        Template Subject
                      </Label>
                      <Input
                        id="templateSubject"
                        value={newTemplate.subject}
                        onChange={(e) =>
                          setNewTemplate({
                            ...newTemplate,
                            subject: e.target.value,
                          })
                        }
                        placeholder="Template subject"
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label
                        htmlFor="templateCategory"
                        className="text-sm font-medium text-gray-700"
                      >
                        Category
                      </Label>
                      <select
                        id="templateCategory"
                        value={newTemplate.category}
                        onChange={(e) =>
                          setNewTemplate({
                            ...newTemplate,
                            category: e.target.value,
                          })
                        }
                        className="w-full mt-1 px-3 py-2 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                      >
                        <option value="General">General</option>
                        <option value="Onboarding">Onboarding</option>
                        <option value="Newsletter">Newsletter</option>
                        <option value="Promotions">Promotions</option>
                        <option value="Announcements">Announcements</option>
                        <option value="Transactional">Transactional</option>
                        <option value="Loyalty">Loyalty</option>
                      </select>
                    </div>
                  </div>
                  <div>
                    <Label
                      htmlFor="templateDescription"
                      className="text-sm font-medium text-gray-700"
                    >
                      Description
                    </Label>
                    <Input
                      id="templateDescription"
                      value={newTemplate.description}
                      onChange={(e) =>
                        setNewTemplate({
                          ...newTemplate,
                          description: e.target.value,
                        })
                      }
                      placeholder="Brief description of this template"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label
                      htmlFor="templateContent"
                      className="text-sm font-medium text-gray-700"
                    >
                      Template Content
                    </Label>
                    <div className="mt-1 mb-2 text-xs text-gray-500">
                      Use variables like <code>{"{{name}}"}</code>,{" "}
                      <code>{"{{unsubscribeLink}}"}</code>, etc. Variables will
                      be automatically detected.
                    </div>
                    <div className="mt-2">
                      <EmailTemplateEditor
                        initialHtml={newTemplate.content}
                        onSave={(html, variables) => {
                          setNewTemplate({
                            ...newTemplate,
                            content: html,
                          });
                        }}
                        onPreview={(html) => {
                          setPreviewContent(html);

                          // Open the preview tab
                          const composeTab =
                            document.querySelector('[value="compose"]');
                          if (composeTab) {
                            (composeTab as HTMLElement).click();
                          }
                        }}
                      />
                    </div>
                  </div>
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button
                      onClick={() => {
                        // Preview the template before saving
                        let previewContent = newTemplate.content;

                        // Extract variables
                        const variableMatches =
                          newTemplate.content.match(/\{\{([^}]+)\}\}/g) || [];
                        const variables = variableMatches.map((match) =>
                          match.replace(/\{\{|\}\}/g, "")
                        );

                        // Replace variables with sample values for preview
                        variables.forEach((variable) => {
                          let sampleValue = "Sample Value";

                          // Special cases for common variable types
                          if (
                            variable.toLowerCase().includes("date") ||
                            variable.toLowerCase().includes("expiry")
                          ) {
                            sampleValue = "June 30, 2024";
                          } else if (
                            variable.toLowerCase().includes("price") ||
                            variable.toLowerCase().includes("amount")
                          ) {
                            sampleValue = "$49.99";
                          } else if (
                            variable.toLowerCase().includes("percent") ||
                            variable.toLowerCase().includes("discount")
                          ) {
                            sampleValue = "25%";
                          } else if (
                            variable.toLowerCase().includes("restaurant")
                          ) {
                            sampleValue = "Delicious Bistro";
                          } else if (
                            variable.toLowerCase().includes("image") ||
                            variable.toLowerCase().includes("img")
                          ) {
                            sampleValue = "https://via.placeholder.com/300x200";
                          } else if (
                            variable.toLowerCase().includes("link") ||
                            variable.toLowerCase().includes("url")
                          ) {
                            sampleValue = "#";
                          } else if (
                            variable.toLowerCase().includes("description")
                          ) {
                            sampleValue =
                              "This is a sample description text that would appear in your email.";
                          } else if (variable.toLowerCase().includes("title")) {
                            sampleValue = "Sample Title";
                          } else if (variable.toLowerCase().includes("month")) {
                            sampleValue = "June";
                          }

                          // Replace all occurrences of this variable
                          const regex = new RegExp(
                            `\\{\\{${variable}\\}\\}`,
                            "g"
                          );
                          previewContent = previewContent.replace(
                            regex,
                            sampleValue
                          );
                        });

                        setPreviewContent(previewContent);

                        // Open the preview tab
                        const composeTab =
                          document.querySelector('[value="compose"]');
                        if (composeTab) {
                          (composeTab as HTMLElement).click();
                        }
                      }}
                      variant="outline"
                      className="border-gray-300 hover:bg-gray-100"
                    >
                      Preview Template
                    </Button>
                    <Button
                      onClick={saveTemplate}
                      disabled={!newTemplate.subject || !newTemplate.content}
                      className="bg-orange-500 hover:bg-orange-600"
                    >
                      Save Template
                    </Button>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-4">
                    Saved Templates
                  </h3>

                  {/* Category filter */}
                  <div className="mb-4">
                    <Label
                      htmlFor="categoryFilter"
                      className="text-sm font-medium text-gray-700 mb-1 block"
                    >
                      Filter by Category
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        variant={!selectedCategory ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedCategory("")}
                        className={
                          !selectedCategory
                            ? "bg-orange-500 hover:bg-orange-600"
                            : ""
                        }
                      >
                        All
                      </Button>
                      {Array.from(
                        new Set(templates.map((t) => t.category || "General"))
                      ).map((category) => (
                        <Button
                          key={category}
                          variant={
                            selectedCategory === category
                              ? "default"
                              : "outline"
                          }
                          size="sm"
                          onClick={() => setSelectedCategory(category)}
                          className={
                            selectedCategory === category
                              ? "bg-orange-500 hover:bg-orange-600"
                              : ""
                          }
                        >
                          {category}
                        </Button>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {templates
                      .filter(
                        (template) =>
                          !selectedCategory ||
                          template.category === selectedCategory
                      )
                      .map((template) => (
                        <Card
                          key={template.id}
                          className="shadow-md hover:shadow-lg transition-shadow"
                        >
                          <CardContent className="p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-medium text-gray-800">
                                {template.subject}
                              </h4>
                              {template.category && (
                                <span className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                                  {template.category}
                                </span>
                              )}
                            </div>

                            {template.description && (
                              <p className="text-sm text-gray-600 mb-2">
                                {template.description}
                              </p>
                            )}

                            {template.variables &&
                              template.variables.length > 0 && (
                                <div className="mb-3">
                                  <p className="text-xs text-gray-500 mb-1">
                                    Variables:
                                  </p>
                                  <div className="flex flex-wrap gap-1">
                                    {template.variables.map((variable) => (
                                      <span
                                        key={variable}
                                        className="inline-block bg-gray-100 text-gray-600 text-xs px-1.5 py-0.5 rounded"
                                      >
                                        {variable}
                                      </span>
                                    ))}
                                  </div>
                                </div>
                              )}

                            <div
                              className="text-xs text-gray-600 mt-2 line-clamp-2 bg-gray-50 p-2 rounded"
                              dangerouslySetInnerHTML={{
                                __html: template.content,
                              }}
                            />

                            <div className="flex gap-2 mt-3">
                              <Button
                                onClick={() => {
                                  // Create a preview with sample data for all variables
                                  let previewContent = template.content;

                                  // Replace common variables first
                                  previewContent = previewContent
                                    .replace(/\{\{name\}\}/g, "Test User")
                                    .replace(/\{\{unsubscribeLink\}\}/g, "#");

                                  // Replace any other variables with sample data
                                  const variableMatches =
                                    template.content.match(
                                      /\{\{([^}]+)\}\}/g
                                    ) || [];
                                  variableMatches.forEach((variable) => {
                                    const variableName = variable.replace(
                                      /\{\{|\}\}/g,
                                      ""
                                    );
                                    if (
                                      variableName !== "name" &&
                                      variableName !== "unsubscribeLink"
                                    ) {
                                      // Generate sample data based on variable name
                                      let sampleValue = "Sample Value";

                                      // Special cases for common variable types
                                      if (
                                        variableName
                                          .toLowerCase()
                                          .includes("date") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("expiry")
                                      ) {
                                        sampleValue = "June 30, 2024";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("price") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("amount")
                                      ) {
                                        sampleValue = "$49.99";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("percent") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("discount")
                                      ) {
                                        sampleValue = "25%";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("restaurant")
                                      ) {
                                        sampleValue = "Delicious Bistro";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("image") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("img")
                                      ) {
                                        sampleValue =
                                          "https://via.placeholder.com/300x200";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("link") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("url")
                                      ) {
                                        sampleValue = "#";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("description")
                                      ) {
                                        sampleValue =
                                          "This is a sample description text that would appear in your email.";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("title")
                                      ) {
                                        sampleValue = "Sample Title";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("month")
                                      ) {
                                        sampleValue = "June";
                                      }

                                      // Replace all occurrences of this variable
                                      const regex = new RegExp(
                                        `\\{\\{${variableName}\\}\\}`,
                                        "g"
                                      );
                                      previewContent = previewContent.replace(
                                        regex,
                                        sampleValue
                                      );
                                    }
                                  });

                                  setPreviewContent(previewContent);

                                  // Open the preview tab
                                  const composeTab =
                                    document.querySelector('[value="compose"]');
                                  if (composeTab) {
                                    (composeTab as HTMLElement).click();
                                  }
                                }}
                                variant="outline"
                                size="sm"
                                className="flex-1 border-gray-300 hover:bg-gray-100"
                              >
                                Preview
                              </Button>
                              <Button
                                onClick={() => {
                                  setEmailSubject(template.subject);
                                  setEmailContent(template.content);

                                  // Update template usage count
                                  const templateRef = doc(
                                    firestore,
                                    "emailTemplates",
                                    template.id
                                  );
                                  updateDoc(templateRef, {
                                    lastUsed: Timestamp.now(),
                                    usageCount: (template.usageCount || 0) + 1,
                                  }).catch((error) => {
                                    console.error(
                                      "Error updating template usage:",
                                      error
                                    );
                                  });

                                  // Create a preview with sample data for all variables
                                  let previewContent = template.content;

                                  // Replace common variables first
                                  previewContent = previewContent
                                    .replace(/\{\{name\}\}/g, "Test User")
                                    .replace(/\{\{unsubscribeLink\}\}/g, "#");

                                  // Replace any other variables with sample data
                                  const variableMatches =
                                    template.content.match(
                                      /\{\{([^}]+)\}\}/g
                                    ) || [];
                                  variableMatches.forEach((variable) => {
                                    const variableName = variable.replace(
                                      /\{\{|\}\}/g,
                                      ""
                                    );
                                    if (
                                      variableName !== "name" &&
                                      variableName !== "unsubscribeLink"
                                    ) {
                                      // Generate sample data based on variable name
                                      let sampleValue = "Sample Value";

                                      // Special cases for common variable types
                                      if (
                                        variableName
                                          .toLowerCase()
                                          .includes("date") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("expiry")
                                      ) {
                                        sampleValue = "June 30, 2024";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("price") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("amount")
                                      ) {
                                        sampleValue = "$49.99";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("percent") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("discount")
                                      ) {
                                        sampleValue = "25%";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("restaurant")
                                      ) {
                                        sampleValue = "Delicious Bistro";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("image") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("img")
                                      ) {
                                        sampleValue =
                                          "https://via.placeholder.com/300x200";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("link") ||
                                        variableName
                                          .toLowerCase()
                                          .includes("url")
                                      ) {
                                        sampleValue = "#";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("description")
                                      ) {
                                        sampleValue =
                                          "This is a sample description text that would appear in your email.";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("title")
                                      ) {
                                        sampleValue = "Sample Title";
                                      } else if (
                                        variableName
                                          .toLowerCase()
                                          .includes("month")
                                      ) {
                                        sampleValue = "June";
                                      }

                                      // Replace all occurrences of this variable
                                      const regex = new RegExp(
                                        `\\{\\{${variableName}\\}\\}`,
                                        "g"
                                      );
                                      previewContent = previewContent.replace(
                                        regex,
                                        sampleValue
                                      );
                                    }
                                  });

                                  setPreviewContent(previewContent);

                                  // Open the compose tab
                                  const composeTab =
                                    document.querySelector('[value="compose"]');
                                  if (composeTab) {
                                    (composeTab as HTMLElement).click();
                                  }

                                  toast.success("Template loaded successfully");
                                }}
                                size="sm"
                                className="flex-1 bg-orange-500 hover:bg-orange-600"
                              >
                                Use Template
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                  </div>

                  {templates.length === 0 && (
                    <div className="text-center py-8 bg-gray-50 rounded-lg">
                      <p className="text-gray-500 mb-2">No templates found</p>
                      <p className="text-sm text-gray-400">
                        Create your first template to get started
                      </p>
                    </div>
                  )}

                  {templates.length > 0 &&
                    selectedCategory &&
                    templates.filter((t) => t.category === selectedCategory)
                      .length === 0 && (
                      <div className="text-center py-8 bg-gray-50 rounded-lg">
                        <p className="text-gray-500 mb-2">
                          No templates in this category
                        </p>
                        <p className="text-sm text-gray-400">
                          Create a template in this category or select a
                          different category
                        </p>
                      </div>
                    )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="subscribers" className="mt-6">
              <div>
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-lg font-medium text-gray-800">
                    Subscribers ({subscribers.length})
                  </h3>
                  <Button
                    variant="outline"
                    onClick={() => {
                      const loadingToast = toast.loading(
                        "Refreshing subscribers..."
                      );
                      const apiUrl =
                        process.env.NODE_ENV === "production"
                          ? "https://api.qonai.me/subscribers"
                          : "http://localhost:3000/subscribers";

                      fetch(apiUrl)
                        .then((res) => res.json())
                        .then((data) => {
                          setSubscribers(data);
                          toast.dismiss(loadingToast);
                          toast.success(
                            `Refreshed subscribers (${data.length})`
                          );
                        })
                        .catch((err) => {
                          toast.dismiss(loadingToast);
                          toast.error(`Failed to refresh: ${err.message}`);
                        });
                    }}
                    className="text-sm"
                  >
                    Refresh
                  </Button>
                </div>

                {subscribers.length === 0 ? (
                  <div className="text-center py-8 bg-gray-50 rounded-lg">
                    <p className="text-gray-500 mb-2">No subscribers found</p>
                    <p className="text-sm text-gray-400">
                      Subscribe to the newsletter to see subscribers here
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {subscribers.map((subscriber) => (
                      <div
                        key={subscriber.id}
                        className="flex items-center justify-between p-4 bg-gray-50 rounded-lg shadow-sm"
                      >
                        <div className="flex flex-col">
                          <span className="text-gray-700">
                            {subscriber.email}
                          </span>
                          {subscriber.name && (
                            <span className="text-sm text-gray-500">
                              {subscriber.name}
                            </span>
                          )}
                        </div>
                        <span className="text-sm text-gray-500">
                          {subscriber.timestamp
                            ? formatFirestoreDate(subscriber.timestamp)
                            : formatFirestoreDate(
                                subscriber.createdAt || subscriber.subscribedAt
                              )}
                        </span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Export subscribers button */}
                {subscribers.length > 0 && (
                  <div className="mt-6">
                    <Button
                      variant="outline"
                      onClick={() => {
                        // Create CSV content
                        const csvContent = [
                          ["Email", "Name", "Subscribed Date"].join(","),
                          ...subscribers.map((sub) =>
                            [
                              sub.email,
                              sub.name || "",
                              sub.timestamp
                                ? formatFirestoreDate(sub.timestamp)
                                : formatFirestoreDate(
                                    sub.createdAt || sub.subscribedAt
                                  ),
                            ].join(",")
                          ),
                        ].join("\n");

                        // Create download link
                        const blob = new Blob([csvContent], {
                          type: "text/csv",
                        });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement("a");
                        a.href = url;
                        a.download = `subscribers-${
                          new Date().toISOString().split("T")[0]
                        }.csv`;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        URL.revokeObjectURL(url);

                        toast.success("Subscribers exported to CSV");
                      }}
                      className="w-full"
                    >
                      Export to CSV
                    </Button>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </section>
  );
}
