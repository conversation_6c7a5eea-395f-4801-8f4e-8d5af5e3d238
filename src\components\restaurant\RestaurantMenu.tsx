import { useState, useMemo, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { MenuItem } from "@/types/restaurant";
import {
  ShoppingCart,
  Plus,
  ArrowUp,
  ArrowDown,
  Coffee,
  Utensils,
  Salad,
  Sandwich,
  IceCream,
  Cake,
  Pizza,
  Beef,
  AlertCircle,
  X,
  Share2,
  Info,
  Leaf,
} from "lucide-react";
import { toast } from "sonner";
import { notificationService } from "@/services/NotificationService";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ShareableCard } from "@/components/ui/shareable-card";
import { getMenuItemShareUrl, generateHashtags } from "@/utils/shareUtils";
import { MetaTags } from "@/components/ui/meta-tags";
import { NutritionInfo } from "./NutritionInfo";

const CATEGORY_GROUPS: Record<
  string,
  { categories: string[]; icon: React.ElementType }
> = {
  "Main Dishes": {
    categories: ["Main Courses", "Grilled Dishes", "Seafood", "Pasta"],
    icon: Utensils,
  },
  Starters: { categories: ["Appetizers", "Soups", "Salads"], icon: Salad },
  "Quick Bites": {
    categories: ["Sandwiches", "Burgers", "Pizza"],
    icon: Sandwich,
  },
  "Healthy Options": {
    categories: ["Vegetarian", "Vegan", "Side Dishes"],
    icon: Beef,
  }, // Using Beef icon as placeholder
  Drinks: {
    categories: ["Beverages", "Hot Drinks", "Cold Drinks"],
    icon: Coffee,
  },
  "Special Menus": {
    categories: ["Breakfast", "Kids Menu", "Special Menu"],
    icon: Pizza,
  }, // Using Pizza icon as placeholder
  Desserts: { categories: ["Desserts"], icon: IceCream },
};

interface CartItem {
  item: MenuItem;
  quantity: number;
  notes?: string;
}

interface RestaurantMenuProps {
  menu: MenuItem[];
  loading: boolean;
  restaurantId: string;
  selectedItemId?: string | null;
}

export const RestaurantMenu = ({
  menu,
  loading,
  restaurantId,
  selectedItemId,
}: RestaurantMenuProps) => {
  const navigate = useNavigate();
  const restaurantUsername = window.location.pathname.split("/")[2];

  // Handle deep linking for menu items
  useEffect(() => {
    if (selectedItemId && menu.length > 0) {
      const selectedItem = menu.find((item) => item.id === selectedItemId);
      if (selectedItem) {
        // Find the category of the selected item
        const category = selectedItem.category;
        const groupCategory =
          Object.keys(CATEGORY_GROUPS).find((group) =>
            CATEGORY_GROUPS[group].categories.includes(category)
          ) || category;

        // Set the selected category
        setSelectedCategory(groupCategory);

        // Open the item dialog after a short delay to ensure the menu is rendered
        setTimeout(() => {
          const itemCard = document.getElementById(
            `menu-item-${selectedItemId}`
          );
          if (itemCard) {
            itemCard.scrollIntoView({ behavior: "smooth", block: "center" });
            itemCard.click();
          }
        }, 500);
      }
    }
  }, [selectedItemId, menu]);
  useEffect(() => {
    if (!restaurantId && menu.length > 0) {
      console.warn("Restaurant ID is missing but menu items are available");
    }
  }, [restaurantId, menu]);

  useEffect(() => {
    notificationService.requestNotificationPermission();
  }, []);

  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [sortBy, setSortBy] = useState<"name" | "price">("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [cart, setCart] = useState<CartItem[]>([]);

  const cartItemCount = useMemo(() => {
    return cart.reduce((count, item) => count + item.quantity, 0);
  }, [cart]);

  const menuCategories = useMemo(() => {
    const itemCategories = new Set(menu.map((item) => item.category));
    const groupedCategories: Array<{
      name: string;
      icon: React.ElementType | null;
    }> = [{ name: "all", icon: null }];

    Object.entries(CATEGORY_GROUPS).forEach(([groupName, groupData]) => {
      if (groupData.categories.some((cat) => itemCategories.has(cat))) {
        groupedCategories.push({ name: groupName, icon: groupData.icon });
      }
    });

    itemCategories.forEach((cat) => {
      if (
        !groupedCategories.some(
          (gc) =>
            gc.name === cat ||
            (gc.name !== "all" &&
              CATEGORY_GROUPS[gc.name]?.categories.includes(cat))
        )
      ) {
        groupedCategories.push({ name: cat, icon: Cake }); // Default icon for ungrouped
      }
    });

    return groupedCategories;
  }, [menu]);

  const filteredMenu = useMemo(() => {
    // Add restaurant username to each menu item
    const menuWithUsername = menu.map((item) => ({
      ...item,
      restaurantUsername: item.restaurantUsername || restaurantUsername,
    }));

    if (selectedCategory === "all") return menuWithUsername;
    if (selectedCategory in CATEGORY_GROUPS) {
      return menuWithUsername.filter((item) =>
        CATEGORY_GROUPS[selectedCategory].categories.includes(item.category)
      );
    }
    return menuWithUsername.filter(
      (item) => item.category === selectedCategory
    );
  }, [menu, selectedCategory, restaurantUsername]);

  const sortedMenu = useMemo(() => {
    return [...filteredMenu].sort((a, b) => {
      if (sortBy === "name") {
        return sortOrder === "asc"
          ? a.name.localeCompare(b.name)
          : b.name.localeCompare(a.name);
      } else {
        return sortOrder === "asc" ? a.price - b.price : b.price - a.price;
      }
    });
  }, [filteredMenu, sortBy, sortOrder]);

  // Load cart from localStorage on component mount
  useEffect(() => {
    if (!restaurantId) return;

    const storedCart = localStorage.getItem(`cart_${restaurantId}`);
    if (storedCart) {
      setCart(JSON.parse(storedCart));
    }
  }, [restaurantId]);

  const addToCart = (item: MenuItem) => {
    if (!item.available) {
      toast.error("This item is currently unavailable");
      return;
    }

    setCart((prevCart) => {
      const existingItem = prevCart.find(
        (cartItem) => cartItem.item.id === item.id
      );

      if (existingItem) {
        return prevCart.map((cartItem) =>
          cartItem.item.id === item.id
            ? { ...cartItem, quantity: cartItem.quantity + 1 }
            : cartItem
        );
      } else {
        return [...prevCart, { item, quantity: 1 }];
      }
    });

    toast.success(`Added ${item.name} to cart`);
  };

  // Save cart to localStorage whenever it changes and dispatch custom event
  useEffect(() => {
    if (restaurantId) {
      if (cart.length > 0) {
        localStorage.setItem(`cart_${restaurantId}`, JSON.stringify(cart));
      } else {
        localStorage.removeItem(`cart_${restaurantId}`);
      }

      // Dispatch a custom event to notify other components about cart changes
      const cartUpdateEvent = new CustomEvent("cart-updated", {
        detail: {
          restaurantId,
          count: cart.reduce((total, item) => total + item.quantity, 0),
        },
      });
      window.dispatchEvent(cartUpdateEvent);
    }
  }, [cart, restaurantId]);

  return (
    <div className="w-full">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3">
        <h1 className="text-2xl sm:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/60">
          Menu
        </h1>
        <Button
          variant="outline"
          className="w-full sm:w-auto h-10 sm:h-11 text-sm sm:text-base"
          onClick={() => {
            const username = window.location.pathname.split("/")[2];
            navigate(`/restaurants/${username}/cart`);
          }}
        >
          <ShoppingCart className="mr-1.5 sm:mr-2 h-4 w-4 sm:h-5 sm:w-5" />
          <span>
            {cartItemCount > 0 ? (
              <span>
                Current Sale{" "}
                <span className="font-semibold">({cartItemCount})</span>
              </span>
            ) : (
              <span>View Cart</span>
            )}
          </span>
        </Button>
      </div>

      <div className="sticky top-4 z-10 bg-background/90 backdrop-blur supports-[backdrop-filter]:bg-background/60 border rounded-lg p-3 sm:p-4 shadow-sm mb-4 sm:mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 mb-3">
          <h2 className="text-lg sm:text-xl font-semibold">Filter & Sort</h2>
          <div className="flex gap-1.5 sm:gap-2 items-center w-full sm:w-auto">
            <Select
              value={sortBy}
              onValueChange={(value: "name" | "price") => setSortBy(value)}
            >
              <SelectTrigger className="w-full sm:w-[120px] h-8 sm:h-9 text-xs">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name" className="text-xs">
                  Name
                </SelectItem>
                <SelectItem value="price" className="text-xs">
                  Price
                </SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="outline"
              size="icon"
              onClick={() =>
                setSortOrder((prev) => (prev === "asc" ? "desc" : "asc"))
              }
              className="w-8 h-8 sm:w-9 sm:h-9 flex-shrink-0"
            >
              {sortOrder === "asc" ? (
                <ArrowUp className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              ) : (
                <ArrowDown className="h-3.5 w-3.5 sm:h-4 sm:w-4" />
              )}
            </Button>
          </div>
        </div>

        <div className="w-full overflow-x-auto pb-2">
          <div className="flex gap-1.5 sm:gap-2 pb-1 sm:pb-2 min-w-max">
            {menuCategories.map(({ name: category, icon: Icon }) => (
              <Button
                key={category}
                variant={selectedCategory === category ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="capitalize whitespace-nowrap text-xs h-7 sm:h-8 px-2 sm:px-3 flex-shrink-0"
              >
                {Icon && (
                  <Icon className="mr-1 sm:mr-1.5 h-3 w-3 sm:h-3.5 sm:w-3.5" />
                )}
                {category}
              </Button>
            ))}
          </div>
        </div>
      </div>

      {loading ? (
        <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="overflow-hidden">
              <div className="aspect-[4/3] w-full bg-muted animate-pulse max-h-[300px]"></div>
              <CardHeader className="p-3 sm:p-4">
                <div className="space-y-2">
                  <div className="h-4 sm:h-5 w-3/4 bg-muted animate-pulse rounded"></div>
                  <div className="h-3 sm:h-4 w-full bg-muted animate-pulse rounded"></div>
                  <div className="h-3 sm:h-4 w-1/2 bg-muted animate-pulse rounded"></div>
                </div>
              </CardHeader>
            </Card>
          ))}
        </div>
      ) : sortedMenu.length === 0 ? (
        <div className="text-center py-12">
          <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-muted mb-4">
            <Utensils className="w-8 h-8 text-muted-foreground" />
          </div>
          <p className="text-muted-foreground text-lg font-medium">
            {selectedCategory === "all"
              ? "No menu items available at the moment"
              : `No items found in the "${selectedCategory}" category`}
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Try selecting a different category or check back later.
          </p>
        </div>
      ) : (
        <div className="space-y-12">
          {selectedCategory === "all" ? (
            Object.entries(
              sortedMenu.reduce((acc, item) => {
                const groupCategory =
                  Object.keys(CATEGORY_GROUPS).find((group) =>
                    CATEGORY_GROUPS[group].categories.includes(item.category)
                  ) || item.category;
                if (!acc[groupCategory]) {
                  acc[groupCategory] = [];
                }
                acc[groupCategory].push(item);
                return acc;
              }, {} as Record<string, MenuItem[]>)
            )
              .sort(([catA], [catB]) => {
                // Sort categories based on predefined order or alphabetically
                const orderA = Object.keys(CATEGORY_GROUPS).indexOf(catA);
                const orderB = Object.keys(CATEGORY_GROUPS).indexOf(catB);
                if (orderA !== -1 && orderB !== -1) return orderA - orderB;
                if (orderA !== -1) return -1;
                if (orderB !== -1) return 1;
                return catA.localeCompare(catB);
              })
              .map(([category, items]) => {
                if (items.length === 0) return null;
                const Icon = CATEGORY_GROUPS[category]?.icon || Cake; // Fallback icon
                return (
                  <div
                    key={category}
                    id={category.toLowerCase().replace(/\s+/g, "-")}
                    className="scroll-mt-24"
                  >
                    <h3 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6 capitalize flex items-center gap-1.5 sm:gap-2">
                      <Icon className="h-4 w-4 sm:h-5 sm:w-5 text-primary" />
                      {category}
                    </h3>
                    <div className="grid sm:grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                      {items.map((item) => (
                        <MenuItemCard
                          key={item.id}
                          item={item}
                          onAddToCart={() => addToCart(item)}
                        />
                      ))}
                    </div>
                  </div>
                );
              })
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              {sortedMenu.map((item) => (
                <MenuItemCard
                  key={item.id}
                  item={item}
                  onAddToCart={() => addToCart(item)}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

interface MenuItemDialogProps {
  item: MenuItem;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddToCart: () => void;
}

const MenuItemDialog = ({
  item,
  open,
  onOpenChange,
  onAddToCart,
}: MenuItemDialogProps) => {
  // State for share dialog
  const [showShareDialog, setShowShareDialog] = useState(false);

  return (
    <Dialog modal={false} open={open} onOpenChange={onOpenChange}>
      {/* Add meta tags for social sharing */}
      {open && (
        <MetaTags
          title={`${item.name} | Qonai Food Ordering`}
          description={item.description || `${item.name} - ${item.category}`}
          imageUrl={item.imageUrl}
          url={getMenuItemShareUrl(
            item.restaurantUsername || window.location.pathname.split("/")[2],
            item.id || item.itemId
          )}
          type="food"
        />
      )}

      <DialogContent
        className="sm:max-w-xl max-h-[90vh] flex flex-col p-0"
        hideCloseButton
      >
        <DialogHeader className="p-6 pb-4 border-b">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <DialogTitle className="text-xl sm:text-2xl flex flex-wrap items-center gap-x-3 gap-y-1">
                {item.name}
                {item.isSignatureDish && (
                  <Badge
                    variant="secondary"
                    className="bg-amber-100 text-amber-800 text-xs"
                  >
                    Signature
                  </Badge>
                )}
                {item.isSeasonalDish && (
                  <Badge
                    variant="secondary"
                    className="bg-teal-100 text-teal-800 text-xs"
                  >
                    Seasonal
                  </Badge>
                )}
              </DialogTitle>
              {item.description && (
                <DialogDescription className="text-sm sm:text-base pt-1">
                  {item.description}
                </DialogDescription>
              )}
            </div>

            <Button
              variant="ghost"
              size="icon"
              onClick={() => setShowShareDialog(true)}
              className="flex-shrink-0"
            >
              <Share2 className="h-5 w-5" />
            </Button>
          </div>
        </DialogHeader>

        {/* Share Dialog */}
        <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
          <DialogContent className="sm:max-w-md" hideCloseButton>
            <DialogHeader className="mb-4">
              <DialogTitle className="text-xl">Share Dish</DialogTitle>
              <DialogDescription>
                Share {item.name} with your friends and family
              </DialogDescription>
            </DialogHeader>

            <ShareableCard
              title={item.name}
              description={item.description || ""}
              imageUrl={item.imageUrl}
              url={getMenuItemShareUrl(
                item.restaurantUsername ||
                  window.location.pathname.split("/")[2],
                item.id || item.itemId
              )}
              hashtags={generateHashtags(
                ["Qonai", "Food", "Dish"],
                item.dietary || []
              )}
            >
              <div className="flex justify-between items-center mt-2 pt-2 border-t">
                <div className="flex flex-wrap gap-1.5">
                  {item.dietary?.slice(0, 2).map((diet, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {diet}
                    </Badge>
                  ))}
                </div>
                <span className="font-bold">{item.price.toFixed(2)} AZN</span>
              </div>
            </ShareableCard>
          </DialogContent>
        </Dialog>

        <ScrollArea className="flex-1 overflow-y-auto px-6 pt-4">
          <div className="space-y-5 pb-6">
            <div className="relative h-[250px] sm:h-[300px] max-h-[400px] rounded-lg overflow-hidden border">
              {item.imageUrl ? (
                <img
                  src={item.imageUrl}
                  alt={item.name}
                  className="w-full h-full object-cover"
                  referrerPolicy="no-referrer"
                  onError={(e) => {
                    console.log("Image failed to load:", item.imageUrl);
                    e.currentTarget.src = "/placeholder-food.jpg";
                  }}
                  loading="lazy"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center bg-muted">
                  <span className="text-muted-foreground text-lg">
                    {item.name}
                  </span>
                </div>
              )}
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 gap-x-4 gap-y-4 text-sm">
              <div>
                <h4 className="font-medium text-xs text-muted-foreground mb-1 uppercase tracking-wider">
                  Category
                </h4>
                <Badge
                  variant="outline"
                  className="capitalize text-xs sm:text-sm"
                >
                  {item.category}
                </Badge>
              </div>
              <div>
                <h4 className="font-medium text-xs text-muted-foreground mb-1 uppercase tracking-wider">
                  Status
                </h4>
                <Badge
                  variant={item.available ? "default" : "destructive"}
                  className="text-xs sm:text-sm"
                >
                  {item.available ? "Available" : "Not Available"}
                </Badge>
              </div>
              {item.spicyLevel && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-1 uppercase tracking-wider">
                    Spicy Level
                  </h4>
                  <Badge
                    variant="outline"
                    className="capitalize text-xs sm:text-sm"
                  >
                    {item.spicyLevel}
                  </Badge>
                </div>
              )}
              {item.calories && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-1 uppercase tracking-wider">
                    Calories
                  </h4>
                  <p className="font-medium">{item.calories} kcal</p>
                </div>
              )}
              {item.preparationTime && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-1 uppercase tracking-wider">
                    Prep Time
                  </h4>
                  <p className="font-medium">{item.preparationTime}</p>
                </div>
              )}
            </div>

            <div className="space-y-4 pt-2">
              {item.ingredients && item.ingredients.length > 0 && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-2 uppercase tracking-wider">
                    Ingredients
                  </h4>
                  <div className="flex flex-wrap gap-1.5">
                    {item.ingredients.map((ingredient, index) => (
                      <Badge
                        key={index}
                        variant="secondary"
                        className="capitalize text-xs font-normal"
                      >
                        {ingredient}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Nutrition Information */}
              {(item.calories !== undefined ||
                item.protein !== undefined ||
                item.carbs !== undefined ||
                item.fat !== undefined) && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-2 uppercase tracking-wider flex items-center">
                    Nutrition Information
                    <NutritionInfo
                      item={item}
                      trigger={
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-0 h-4 w-4 ml-1 text-muted-foreground hover:text-foreground"
                        >
                          <Info className="h-3 w-3" />
                        </Button>
                      }
                    />
                  </h4>
                  <div className="grid grid-cols-4 gap-2">
                    {item.calories !== undefined && (
                      <div className="space-y-1 text-center p-1 bg-muted/30 rounded">
                        <p className="text-xs text-muted-foreground">
                          Calories
                        </p>
                        <p className="font-medium text-sm">{item.calories}</p>
                      </div>
                    )}
                    {item.protein !== undefined && (
                      <div className="space-y-1 text-center p-1 bg-muted/30 rounded">
                        <p className="text-xs text-muted-foreground">Protein</p>
                        <p className="font-medium text-sm">{item.protein}g</p>
                      </div>
                    )}
                    {item.carbs !== undefined && (
                      <div className="space-y-1 text-center p-1 bg-muted/30 rounded">
                        <p className="text-xs text-muted-foreground">Carbs</p>
                        <p className="font-medium text-sm">{item.carbs}g</p>
                      </div>
                    )}
                    {item.fat !== undefined && (
                      <div className="space-y-1 text-center p-1 bg-muted/30 rounded">
                        <p className="text-xs text-muted-foreground">Fat</p>
                        <p className="font-medium text-sm">{item.fat}g</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Health Labels */}
              {item.healthLabels && item.healthLabels.length > 0 && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-2 uppercase tracking-wider">
                    Health Labels
                  </h4>
                  <div className="flex flex-wrap gap-1.5">
                    {item.healthLabels.map((label, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="capitalize text-xs font-normal border-blue-300 bg-blue-50 text-blue-700"
                      >
                        {label}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {item.dietary && item.dietary.length > 0 && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-2 uppercase tracking-wider flex items-center">
                    Dietary Info
                    <Leaf className="h-3 w-3 ml-1 text-green-600" />
                  </h4>
                  <div className="flex flex-wrap gap-1.5">
                    {item.dietary.map((diet, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="capitalize text-xs font-normal border-green-300 bg-green-50 text-green-700"
                      >
                        {diet}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {item.allergens && item.allergens.length > 0 && (
                <div>
                  <h4 className="font-medium text-xs text-muted-foreground mb-2 uppercase tracking-wider flex items-center">
                    Allergens
                    <AlertCircle className="h-3 w-3 ml-1 text-red-600" />
                  </h4>
                  <div className="flex flex-wrap gap-1.5">
                    {item.allergens.map((allergen, index) => (
                      <Badge
                        key={index}
                        variant="outline"
                        className="capitalize text-xs font-normal border-red-300 bg-red-50 text-red-700"
                      >
                        {allergen}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </ScrollArea>

        <DialogFooter className="p-6 pt-4 border-t bg-muted/30">
          <div className="flex justify-between items-center w-full">
            <p className="text-xl font-bold">{item.price.toFixed(2)} AZN</p>
            <Button
              onClick={() => {
                onAddToCart();
                onOpenChange(false);
              }}
              disabled={!item.available}
              size="lg"
              className="h-11"
            >
              {!item.available ? (
                <AlertCircle className="mr-2 h-4 w-4" />
              ) : (
                <ShoppingCart className="mr-2 h-4 w-4" />
              )}
              {item.available ? "Add to Cart" : "Not Available"}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

interface MenuItemCardProps {
  item: MenuItem;
  onAddToCart: () => void;
}

const MenuItemCard = ({ item, onAddToCart }: MenuItemCardProps) => {
  const [showDetails, setShowDetails] = useState(false);

  return (
    <>
      <Card className="overflow-hidden group transition-all duration-300 flex flex-col h-full border hover:border-primary/50 hover:shadow-md">
        <button
          id={`menu-item-${item.id || item.itemId}`}
          className="relative aspect-[4/3] w-full overflow-hidden cursor-pointer focus:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 rounded-t-lg max-h-[300px]"
          onClick={() => setShowDetails(true)}
          aria-label={`View details for ${item.name}`}
        >
          {item.imageUrl ? (
            <img
              src={item.imageUrl}
              alt={item.name}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
              referrerPolicy="no-referrer"
              onError={(e) => {
                console.log("Card image failed to load:", item.imageUrl);
                e.currentTarget.src = "/placeholder-food.jpg";
              }}
              loading="lazy"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-muted">
              <span className="text-muted-foreground">{item.name}</span>
            </div>
          )}
          {!item.available && (
            <div className="absolute inset-0 bg-black/60 flex items-center justify-center backdrop-blur-sm">
              <Badge variant="destructive" className="text-xs">
                Not Available
              </Badge>
            </div>
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end justify-center pb-2">
            <Badge className="bg-primary/90 hover:bg-primary text-primary-foreground text-xs">
              View Details
            </Badge>
          </div>
        </button>
        <CardHeader className="p-3 sm:p-4 flex-1 flex flex-col justify-between">
          <div
            className="flex-1 mb-2 cursor-pointer"
            onClick={() => setShowDetails(true)}
          >
            <CardTitle className="text-sm sm:text-base font-semibold group-hover:text-primary transition-colors duration-200 line-clamp-1">
              {item.name}
            </CardTitle>
            <CardDescription className="line-clamp-2 mt-0.5 sm:mt-1 text-xs">
              {item.description || (
                <span className="italic">No description available.</span>
              )}
            </CardDescription>
          </div>

          {/* Dietary indicators */}
          <div className="flex flex-wrap gap-1 mt-2">
            {item.dietary?.slice(0, 3).map((diet, index) => (
              <Badge
                key={index}
                variant="outline"
                className="text-[10px] px-1 py-0 h-4 border-green-300 bg-green-50 text-green-700"
              >
                {diet}
              </Badge>
            ))}
            {item.allergens && item.allergens.length > 0 && (
              <Badge
                variant="outline"
                className="text-[10px] px-1 py-0 h-4 border-red-300 bg-red-50 text-red-700"
              >
                {item.allergens.length}{" "}
                {item.allergens.length === 1 ? "allergen" : "allergens"}
              </Badge>
            )}
          </div>

          <div className="flex justify-between items-end gap-1 sm:gap-2 mt-auto pt-1 sm:pt-2">
            <div className="flex-shrink-0">
              <div className="flex items-center gap-1">
                <p className="font-bold text-sm sm:text-base whitespace-nowrap">
                  {item.price.toFixed(2)} AZN
                </p>
                {item.calories !== undefined && (
                  <span className="text-xs text-muted-foreground">
                    • {item.calories} kcal
                  </span>
                )}
              </div>
              <div className="flex items-center gap-1 mt-0.5">
                <p className="text-xs text-muted-foreground capitalize">
                  {item.category}
                </p>
                {(item.calories !== undefined ||
                  item.protein !== undefined ||
                  item.carbs !== undefined ||
                  item.fat !== undefined) && (
                  <NutritionInfo
                    item={item}
                    trigger={
                      <Button
                        variant="ghost"
                        size="sm"
                        className="p-0 h-4 w-4 text-muted-foreground hover:text-foreground"
                      >
                        <Info className="h-3 w-3" />
                      </Button>
                    }
                  />
                )}
              </div>
            </div>

            <Button
              variant={item.available ? "default" : "outline"}
              size="sm"
              className={`h-7 sm:h-8 text-xs px-2 sm:px-3 ${
                !item.available
                  ? "cursor-not-allowed text-muted-foreground"
                  : ""
              }`}
              onClick={(e: React.MouseEvent) => {
                if (item.available) {
                  e.stopPropagation();
                  onAddToCart();
                }
              }}
              disabled={!item.available}
              aria-label={
                item.available
                  ? `Add ${item.name} to cart`
                  : `${item.name} is unavailable`
              }
            >
              {!item.available ? (
                <X className="h-3.5 w-3.5 sm:h-4 sm:w-4 sm:mr-1" />
              ) : (
                <Plus className="h-3.5 w-3.5 sm:h-4 sm:w-4 sm:mr-1" />
              )}
              <span className="hidden sm:inline">
                {item.available ? "Add" : "Unavailable"}
              </span>
            </Button>
          </div>
        </CardHeader>
      </Card>
      <MenuItemDialog
        item={item}
        open={showDetails}
        onOpenChange={setShowDetails}
        onAddToCart={onAddToCart}
      />
    </>
  );
};
