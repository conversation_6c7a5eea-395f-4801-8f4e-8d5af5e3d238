// src/App.tsx
import {
  BrowserRouter,
  Routes,
  Route,
  useNavigate,
  useLocation,
  useParams,
  useSearchParams,
} from "react-router-dom";
import {
  useEffect,
  useState,
  ReactNode,
  lazy,
  Suspense,
  startTransition,
} from "react"; // Added startTransition
import { doc, getDoc } from "firebase/firestore";
import { firestore } from "@/config/firebase";
import { useAuth } from "@/providers/AuthProvider";
import { AuthProvider } from "./providers/AuthProvider";
import { OfflineProvider } from "./providers/OfflineProvider";
import { HelmetProvider } from "react-helmet-async";

import { Loading } from "@/components/ui/loading";
import { Button } from "./components/ui/button";
import { Header } from "./components/Header";
import { Footer } from "./components/Footer";
import { ScrollToTop } from "./components/ScrollToTop";
import { AuthGuard } from "./components/AuthGuard";
import { ScheduledOrderProcessor } from "./components/ScheduledOrderProcessor";
import { ErrorBoundary } from "./components/ErrorBoundary"; // Import ErrorBoundary

import { Home } from "./pages/Home";
import { Pricing } from "./pages/Pricing";
import Success from "./pages/Success";
import Failed from "./pages/Failed";
import About from "./pages/About";

import { notificationService } from "./services/NotificationService";
import { toast } from "sonner";

// Simple redirect component for /register path
const RegisterRedirect = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const referralCode = searchParams.get("ref");
    navigate("/auth", {
      state: {
        activeTab: "register",
        referralCode: referralCode || undefined,
      },
    });
  }, [navigate, searchParams]);

  return <Loading text="Redirecting to registration..." />;
};

// Lazy-loaded components
// Ensure export types (default vs named) match these import styles
const LoginRegister = lazy(() =>
  import("./pages/LoginRegister").then((module) => ({
    default: module.LoginRegister, // Assumes LoginRegister is a named export
  }))
);
const Profile = lazy(() =>
  import("./pages/Profile").then((module) => ({
    default: module.Profile, // Assumes Profile is a named export
  }))
);
const Dashboard = lazy(() =>
  import("./pages/Dashboard").then((module) => ({
    default: module.Dashboard, // Assumes Dashboard is a named export
  }))
);
const LoyaltyProgram = lazy(() =>
  import("./pages/LoyaltyProgram").then((module) => ({
    default: module.LoyaltyProgram, // Assumes LoyaltyProgram is a named export
  }))
);
const Restaurants = lazy(() =>
  import("./pages/Restaurants").then((module) => ({
    default: module.Restaurants, // Assumes Restaurants is a named export
  }))
);
const AdminPanel = lazy(() =>
  import("./pages/AdminPanel").then((module) => ({
    default: module.AdminPanel, // Assumes AdminPanel is a named export
  }))
);
// This implies AdminTools uses 'export default AdminTools'
const AdminTools = lazy(() => import("./pages/AdminTools"));
const AdminRewards = lazy(() => import("./pages/AdminRewards"));
const Admin = lazy(() => import("./pages/Admin"));
const RestaurantPage = lazy(() =>
  import("./pages/RestaurantPage").then((module) => ({
    default: module.RestaurantPage, // Assumes RestaurantPage is a named export
  }))
);
const Reservations = lazy(() =>
  import("./pages/Reservations").then((module) => ({
    default: module.Reservations, // Assumes Reservations is a named export
  }))
);
const Receipt = lazy(() =>
  import("./pages/Receipt").then((module) => ({
    default: module.Receipt, // Assumes Receipt is a named export
  }))
);
const OrderPage = lazy(() =>
  import("./pages/OrderPage").then((module) => ({
    default: module.OrderPage, // Assumes OrderPage is a named export
  }))
);
const CartPage = lazy(() =>
  import("./pages/CartPage").then((module) => ({
    default: module.CartPage, // Assumes CartPage is a named export
  }))
);
const EmailPanel = lazy(() =>
  import("./components/admin/EmailPanel").then((module) => ({
    default: module.EmailPanel, // Assumes EmailPanel is a named export
  }))
);
const ResubscribePage = lazy(() =>
  import("./pages/ResubscribePage").then((module) => ({
    default: module.ResubscribePage, // Assumes ResubscribePage is a named export
  }))
);
const QRCodeMenu = lazy(() =>
  import("./pages/QRCodeMenu").then((module) => ({
    default: module.QRCodeMenu, // Assumes QRCodeMenu is a named export
  }))
);

// --- Route Protection Components ---

const ProtectedRoute = ({ children }: { children: ReactNode }) => {
  const { user, loading, isProfileComplete } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    let isMounted = true;
    if (!loading && isMounted) {
      if (!user) {
        toast.info("Please log in to access this page.");
        startTransition(() => {
          navigate("/auth", { state: { from: location }, replace: true });
        });
      } else if (
        isProfileComplete === false &&
        location.pathname !== "/profile"
      ) {
        toast.info("Please complete your profile first.");
        startTransition(() => {
          navigate("/profile", { replace: true });
        });
      }
    }
    return () => {
      isMounted = false;
    };
  }, [user, loading, isProfileComplete, navigate, location]);

  if (loading || isProfileComplete === null) {
    return <Loading text="Checking access..." />;
  }

  return user && isProfileComplete ? <>{children}</> : null;
};

const MustBeLoggedInRoute = ({ children }: { children: ReactNode }) => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    let isMounted = true;
    if (!loading && !user && isMounted) {
      toast.info("Please log in to access this page.");
      startTransition(() => {
        navigate("/auth", { state: { from: location }, replace: true });
      });
    }
    return () => {
      isMounted = false;
    };
  }, [user, loading, navigate, location]);

  if (loading) {
    return <Loading text="Verifying authentication..." />;
  }

  return user ? <>{children}</> : null;
};

const ReceiptRoute = ({ children }: { children: ReactNode }) => {
  const { user, userRole, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { restaurantId, orderId } = useParams<{
    restaurantId: string;
    orderId: string;
  }>();
  const [isCheckingAccess, setIsCheckingAccess] = useState(true);

  useEffect(() => {
    let isMounted = true;

    if (!restaurantId || !orderId) {
      console.error("Receipt route parameters missing!");
      if (isMounted) {
        // Should navigate even if unmounting, but good practice
        startTransition(() => {
          navigate("/dashboard", { replace: true });
        });
      }
      return;
    }

    const checkAccess = async () => {
      if (authLoading) return; // Wait for authentication to resolve

      if (!isMounted) return;
      setIsCheckingAccess(true);

      if (!user) {
        toast.info("Please log in to view receipts.");
        if (isMounted) {
          startTransition(() => {
            navigate("/auth", { state: { from: location }, replace: true });
          });
          setIsCheckingAccess(false);
        }
        return;
      }

      try {
        let hasAccess = false;
        if (userRole === "restaurant") {
          hasAccess = user.uid === restaurantId;
        } else if (userRole === "client") {
          const clientOrderRef = doc(
            firestore,
            "clients",
            user.uid,
            "orders",
            orderId
          );
          const clientOrderDoc = await getDoc(clientOrderRef);
          hasAccess =
            clientOrderDoc.exists() &&
            clientOrderDoc.data()?.restaurantId === restaurantId;
        }

        if (!isMounted) return;

        if (!hasAccess) {
          toast.error("You don't have permission to view this receipt.");
          startTransition(() => {
            navigate("/dashboard", { replace: true });
          });
        }
      } catch (error) {
        console.error("Error checking receipt access:", error);
        toast.error("Failed to check receipt access.");
        if (isMounted) {
          startTransition(() => {
            navigate("/dashboard", { replace: true });
          });
        }
      } finally {
        if (isMounted) {
          setIsCheckingAccess(false);
        }
      }
    };

    checkAccess();

    return () => {
      isMounted = false;
    };
  }, [user, userRole, restaurantId, orderId, navigate, location, authLoading]);

  if (authLoading || isCheckingAccess) {
    return <Loading text="Verifying receipt access..." />;
  }

  // Children rendered if access is permitted (useEffect handles redirection otherwise)
  // User must exist for children to render (covered by isCheckingAccess or explicit redirect)
  return user ? <>{children}</> : null;
};

const OrderRoute = ({ children }: { children: ReactNode }) => {
  const { user, userRole, loading: authLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const { orderId } = useParams<{ orderId: string }>();
  const [isCheckingAccess, setIsCheckingAccess] = useState(true);

  useEffect(() => {
    let isMounted = true;

    if (!orderId) {
      console.error("Order route parameter missing!");
      if (isMounted) {
        startTransition(() => {
          navigate("/dashboard", { replace: true });
        });
      }
      return;
    }

    const checkAccess = async () => {
      if (authLoading) return;

      if (!isMounted) return;
      setIsCheckingAccess(true);

      if (!user) {
        toast.info("Please log in to view orders.");
        if (isMounted) {
          startTransition(() => {
            navigate("/auth", { state: { from: location }, replace: true });
          });
          setIsCheckingAccess(false);
        }
        return;
      }

      try {
        let hasAccess = false;
        // Assuming client check first is most common
        const clientOrderRef = doc(
          firestore,
          "clients",
          user.uid,
          "orders",
          orderId
        );
        const clientOrderDoc = await getDoc(clientOrderRef);

        if (clientOrderDoc.exists()) {
          hasAccess = true;
        } else if (userRole === "restaurant") {
          // This assumes the restaurant's UID is the key in the 'restaurants' collection
          // and orders are stored under 'restaurants/{restaurant_uid}/orders/{orderId}'
          const restaurantOrderRef = doc(
            firestore,
            "restaurants",
            user.uid,
            "orders",
            orderId
          );
          const restaurantOrderDoc = await getDoc(restaurantOrderRef);
          hasAccess = restaurantOrderDoc.exists();
        }

        if (!isMounted) return;

        if (!hasAccess) {
          toast.error("You don't have permission to view this order.");
          startTransition(() => {
            navigate("/dashboard", { replace: true });
          });
        }
      } catch (error) {
        console.error("Error checking order access:", error);
        toast.error("Failed to check order access.");
        if (isMounted) {
          startTransition(() => {
            navigate("/dashboard", { replace: true });
          });
        }
      } finally {
        if (isMounted) {
          setIsCheckingAccess(false);
        }
      }
    };

    checkAccess();

    return () => {
      isMounted = false;
    };
  }, [user, userRole, orderId, navigate, location, authLoading]);

  if (authLoading || isCheckingAccess) {
    return <Loading text="Verifying order access..." />;
  }

  return user ? <>{children}</> : null;
};

// --- Main App Component ---
function App() {
  const [isSoundEnabled] = useState(() => {
    if (typeof window !== "undefined") {
      const soundPref = localStorage.getItem("notificationSound");
      return soundPref === null ? true : soundPref === "true"; // Default true if not set
    }
    return true; // Default for SSR or no localStorage
  });

  useEffect(() => {
    notificationService.requestNotificationPermission();
    notificationService.toggleSound(isSoundEnabled);
  }, [isSoundEnabled]); // Runs once on mount to set initial sound state

  return (
    <AuthProvider>
      <OfflineProvider>
        <HelmetProvider>
          <BrowserRouter>
            <ScrollToTop />
            <ScheduledOrderProcessor />
            <Header />
            <main className="main-content min-h-[calc(100vh-var(--header-height,64px)-var(--footer-height,64px))]">
              <ErrorBoundary>
                {" "}
                {/* Wrap Suspense and Routes with ErrorBoundary */}
                <Suspense fallback={<Loading text="Loading page..." />}>
                  <Routes>
                    {/* Public Routes */}
                    <Route path="/" element={<Home />} />
                    <Route
                      path="/auth"
                      element={
                        <AuthGuard>
                          {" "}
                          {/* Prevents logged-in users from accessing /auth */}
                          <LoginRegister />
                        </AuthGuard>
                      }
                    />
                    <Route path="/register" element={<RegisterRedirect />} />
                    <Route path="/restaurants" element={<Restaurants />} />
                    <Route
                      path="/restaurants/:username"
                      element={<RestaurantPage />}
                    />
                    <Route
                      path="/restaurants/:username/menu"
                      element={<QRCodeMenu />}
                    />
                    <Route
                      path="/restaurants/:username/cart"
                      element={
                        <MustBeLoggedInRoute>
                          {" "}
                          {/* Or ProtectedRoute if profile completion needed for cart */}
                          <CartPage />
                        </MustBeLoggedInRoute>
                      }
                    />
                    <Route path="/pricing" element={<Pricing />} />
                    <Route path="/about" element={<About />} />
                    <Route path="/subscribe" element={<ResubscribePage />} />
                    <Route path="/success" element={<Success />} />
                    <Route path="/failed" element={<Failed />} />

                    {/* Routes requiring login (profile completion not checked) */}
                    <Route
                      path="/profile"
                      element={
                        <MustBeLoggedInRoute>
                          <Profile />
                        </MustBeLoggedInRoute>
                      }
                    />
                    <Route
                      path="/loyalty"
                      element={
                        <MustBeLoggedInRoute>
                          <LoyaltyProgram />
                        </MustBeLoggedInRoute>
                      }
                    />

                    {/* Routes requiring login AND completed profile */}
                    <Route
                      path="/dashboard"
                      element={
                        <ProtectedRoute>
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/restaurants/:username/reservations"
                      element={
                        <ProtectedRoute>
                          <Reservations />
                        </ProtectedRoute>
                      }
                    />
                    {/* Admin routes could have an additional AdminRoleGuard or check within ProtectedRoute */}
                    <Route
                      path="/admin-panel"
                      element={
                        <ProtectedRoute>
                          {" "}
                          {/* Add admin role check here if needed */}
                          <AdminPanel />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin"
                      element={
                        <ProtectedRoute>
                          {" "}
                          {/* Admin role check is inside the component */}
                          <Admin />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin-tools"
                      element={
                        <ProtectedRoute>
                          {" "}
                          {/* Add admin role check here if needed */}
                          <AdminTools />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/admin-rewards"
                      element={
                        <ProtectedRoute>
                          {" "}
                          {/* Admin role check is inside the component */}
                          <AdminRewards />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="/email-panel"
                      element={
                        <ProtectedRoute>
                          {" "}
                          {/* Add admin role check here if needed */}
                          <EmailPanel />
                        </ProtectedRoute>
                      }
                    />

                    {/* Routes requiring specific resource access */}
                    <Route
                      path="/receipts/:restaurantId/:orderId"
                      element={
                        <ReceiptRoute>
                          <Receipt />
                        </ReceiptRoute>
                      }
                    />
                    <Route
                      path="/orders/:orderId"
                      element={
                        <OrderRoute>
                          <OrderPage />
                        </OrderRoute>
                      }
                    />

                    {/* Catch-all for 404 Not Found */}
                    <Route path="*" element={<NotFoundPage />} />
                  </Routes>
                </Suspense>
              </ErrorBoundary>
            </main>
            <Footer />
          </BrowserRouter>
        </HelmetProvider>
      </OfflineProvider>
    </AuthProvider>
  );
}

const NotFoundPage = () => {
  const navigate = useNavigate();
  return (
    <div className="mx-auto flex flex-col items-center justify-center min-h-[calc(60vh)] text-center p-4">
      <h1 className="text-4xl font-bold mb-4">404 - Page Not Found</h1>
      <p className="text-muted-foreground mb-6">
        Sorry, the page you are looking for does not exist or you do not have
        permission to access it.
      </p>
      <Button onClick={() => navigate("/")}>Go Home</Button>
    </div>
  );
};

export default App;
